export interface WritingSession {
  id: string;
  documentId: string;
  startTime: Date;
  endTime?: Date;
  wordsWritten: number;
  wordsAtStart: number;
  timeSpent: number; // in minutes
  aiSuggestionsAccepted: number;
  aiSuggestionsDismissed: number;
  aiGenerationsUsed: number;
  focusModeUsed: boolean;
  featuresUsed: string[];
}

export interface DailyStats {
  date: string; // YYYY-MM-DD format
  wordsWritten: number;
  timeSpent: number; // in minutes
  documentsWorkedOn: number;
  sessionsCount: number;
  aiInteractions: number;
  qualityScoreAverage: number;
  streak: number;
}

export interface WritingGoal {
  id: string;
  type: 'daily_words' | 'weekly_words' | 'daily_time' | 'weekly_time' | 'quality_score';
  target: number;
  current: number;
  startDate: Date;
  endDate?: Date;
  isActive: boolean;
  achieved: boolean;
}

export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  unlockedAt?: Date;
  category: 'writing' | 'quality' | 'consistency' | 'ai_usage' | 'collaboration';
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

export interface DocumentAnalytics {
  documentId: string;
  title: string;
  wordCount: number;
  readingTime: number; // in minutes
  qualityScore: WritingQualityScore;
  createdAt: Date;
  lastModified: Date;
  timeSpent: number; // total time in minutes
  revisionsCount: number;
  aiSuggestionsAccepted: number;
  collaborators: number;
  viewCount: number;
  category: string;
}

export interface WritingQualityScore {
  overall: number;
  grammar: number;
  style: number;
  structure: number;
  content: number;
  readability: number;
  inclusiveLanguage: number;
}

export interface AIUsageStats {
  totalSuggestions: number;
  acceptedSuggestions: number;
  dismissedSuggestions: number;
  acceptanceRate: number;
  favoriteAgent: string;
  mostUsedFeature: string;
  timesSaved: number; // estimated time saved in minutes
  qualityImprovement: number; // average quality score improvement
}

export interface ProductivityInsights {
  peakWritingHours: number[];
  averageSessionLength: number;
  mostProductiveDays: string[];
  distractionScore: number;
  focusModeEffectiveness: number;
  optimalBreakInterval: number;
}

export interface WritingStreak {
  current: number;
  longest: number;
  startDate: Date;
  lastWritingDate: Date;
  goalType: 'words' | 'time' | 'documents';
  threshold: number;
}

export interface AnalyticsState {
  sessions: WritingSession[];
  dailyStats: DailyStats[];
  goals: WritingGoal[];
  achievements: Achievement[];
  documentAnalytics: DocumentAnalytics[];
  aiUsageStats: AIUsageStats;
  productivityInsights: ProductivityInsights;
  writingStreak: WritingStreak;
  lastUpdated: Date;
}

export interface AnalyticsActions {
  // Session tracking
  startSession: (documentId: string) => string;
  endSession: (sessionId: string) => void;
  updateSession: (sessionId: string, updates: Partial<WritingSession>) => void;
  
  // Goals
  createGoal: (goal: Omit<WritingGoal, 'id'>) => void;
  updateGoal: (goalId: string, updates: Partial<WritingGoal>) => void;
  deleteGoal: (goalId: string) => void;
  
  // Analytics
  updateDocumentAnalytics: (documentId: string, analytics: Partial<DocumentAnalytics>) => void;
  recordAIInteraction: (type: 'accepted' | 'dismissed' | 'generated', agent?: string) => void;
  updateQualityScore: (documentId: string, score: WritingQualityScore) => void;
  
  // Insights
  calculateInsights: () => void;
  updateStreak: () => void;
  checkAchievements: () => void;
  
  // Data management
  exportData: () => string;
  importData: (data: string) => void;
  clearData: () => void;
}

export type AnalyticsStore = AnalyticsState & AnalyticsActions;

// Chart data types
export interface ChartDataPoint {
  date: string;
  value: number;
  label?: string;
}

export interface TrendData {
  current: number;
  previous: number;
  change: number;
  trend: 'up' | 'down' | 'stable';
}

export interface PeriodStats {
  today: DailyStats;
  thisWeek: DailyStats[];
  thisMonth: DailyStats[];
  comparison: {
    lastWeek: TrendData;
    lastMonth: TrendData;
  };
}