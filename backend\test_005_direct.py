#!/usr/bin/env python3
"""
Direct test of 005_missing_features.sql to identify the exact failing query
"""

import asyncio
import os
import sys
from pathlib import Path
import asyncpg
from dotenv import load_dotenv

async def test_005_directly():
    """Test 005_missing_features.sql file directly with detailed error reporting."""
    
    # Load environment
    load_dotenv(".env")
    database_url = os.getenv("DATABASE_URL")
    
    if not database_url:
        print("❌ No DATABASE_URL found in environment")
        return
    
    conn = None
    try:
        print("🔌 Connecting to database...")
        conn = await asyncpg.connect(database_url, statement_cache_size=0)
        
        # Read the 005 file
        file_path = Path("mock_data/005_missing_features.sql")
        
        if not file_path.exists():
            print(f"❌ File not found: {file_path}")
            return
            
        print(f"📖 Reading {file_path}...")
        with open(file_path, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        print(f"📄 File loaded, size: {len(sql_content)} characters")
        
        # Split into sections to identify which part fails
        sections = sql_content.split("-- =================================================================")
        
        print(f"📝 Found {len(sections)} sections in the file")
        
        # Try to execute the full file first
        print("\n🚀 Attempting to execute full 005_missing_features.sql...")
        
        try:
            async with conn.transaction():
                await conn.execute(sql_content)
            print("✅ 005_missing_features.sql executed successfully!")
            
            # Verify some data was inserted
            print("\n🔍 Verifying data insertion...")
            
            # Check some key tables
            health_metrics_count = await conn.fetchval("SELECT COUNT(*) FROM health_metrics")
            entities_count = await conn.fetchval("SELECT COUNT(*) FROM entities")
            citations_count = await conn.fetchval("SELECT COUNT(*) FROM citations")
            templates_count = await conn.fetchval("SELECT COUNT(*) FROM templates")
            
            print(f"  Health metrics: {health_metrics_count}")
            print(f"  Entities: {entities_count}")
            print(f"  Citations: {citations_count}")
            print(f"  Templates: {templates_count}")
            
            if health_metrics_count > 0 and entities_count > 0:
                print("✅ Data insertion verified successfully!")
            else:
                print("⚠️ Some tables have no data inserted")
                
        except Exception as e:
            print(f"❌ Full file execution failed: {e}")
            
            # If full file fails, try to execute section by section to identify the problem
            print("\n🔍 Executing sections individually to identify the failing section...")
            
            # Extract the DO block content
            do_start = sql_content.find("DO $$")
            do_end = sql_content.find("END $$;") + len("END $$;")
            
            if do_start != -1 and do_end != -1:
                do_block = sql_content[do_start:do_end]
                
                # Split the DO block into variable declarations and INSERT sections
                lines = do_block.split('\n')
                current_section = []
                section_name = "Variable declarations"
                
                for i, line in enumerate(lines):
                    if "-- =================================================================" in line:
                        if current_section and section_name:
                            # Try to execute the current section
                            section_sql = "DO $$\nDECLARE\n" + get_variable_declarations(do_block) + "\nBEGIN\n" + '\n'.join(current_section) + "\nEND $$;"
                            
                            try:
                                print(f"\n  Testing section: {section_name}")
                                async with conn.transaction():
                                    await conn.execute(section_sql)
                                print(f"  ✅ {section_name} - OK")
                            except Exception as section_error:
                                print(f"  ❌ {section_name} - FAILED: {section_error}")
                                print(f"     Failing SQL section:")
                                print(f"     {section_sql[:500]}...")
                                break
                        
                        current_section = []
                        # Get section name from next line
                        if i + 1 < len(lines):
                            section_name = lines[i + 1].strip().replace("-- ", "")
                    else:
                        current_section.append(line)
                
    except Exception as e:
        print(f"❌ Connection or execution error: {e}")
        
    finally:
        if conn:
            await conn.close()
            print("🔌 Database connection closed")

def get_variable_declarations(do_block):
    """Extract variable declarations from the DO block."""
    lines = do_block.split('\n')
    declarations = []
    in_declare = False
    
    for line in lines:
        if "DECLARE" in line:
            in_declare = True
            continue
        elif "BEGIN" in line:
            break
        elif in_declare and line.strip():
            declarations.append(line)
    
    return '\n'.join(declarations)

async def check_prerequisite_data():
    """Check if prerequisite data exists from files 001-004."""
    
    load_dotenv(".env")
    database_url = os.getenv("DATABASE_URL")
    
    conn = None
    try:
        conn = await asyncpg.connect(database_url, statement_cache_size=0)
        
        print("🔍 Checking prerequisite data from files 001-004...")
        
        # Check users
        users = await conn.fetch("SELECT firebase_uid, display_name FROM users ORDER BY firebase_uid")
        print(f"👤 Users ({len(users)}):")
        for user in users:
            print(f"  - {user['firebase_uid']}: {user['display_name']}")
        
        # Check documents
        docs = await conn.fetch("SELECT title, owner_id FROM documents ORDER BY title")
        print(f"📄 Documents ({len(docs)}):")
        for doc in docs[:10]:  # Show first 10
            print(f"  - {doc['title']}")
        
        # Check personas
        personas = await conn.fetch("SELECT name, user_id FROM personas ORDER BY name")
        print(f"🎭 Personas ({len(personas)}):")
        for persona in personas:
            print(f"  - {persona['name']}")
        
        # Check custom agents
        agents = await conn.fetch("SELECT name, user_id FROM custom_agents ORDER BY name")
        print(f"🤖 Custom Agents ({len(agents)}):")
        for agent in agents:
            print(f"  - {agent['name']}")
            
        return len(users) > 0 and len(docs) > 0 and len(personas) > 0 and len(agents) > 0
        
    except Exception as e:
        print(f"❌ Error checking prerequisite data: {e}")
        return False
        
    finally:
        if conn:
            await conn.close()

async def main():
    """Main test function."""
    print("🧪 Testing 005_missing_features.sql directly")
    print("=" * 60)
    
    # First check if prerequisite data exists
    prereq_ok = await check_prerequisite_data()
    
    if not prereq_ok:
        print("\n❌ Prerequisite data missing. Run files 001-004 first.")
        return
    
    print("\n✅ Prerequisite data looks good. Testing 005_missing_features.sql...")
    
    await test_005_directly()

if __name__ == "__main__":
    asyncio.run(main())