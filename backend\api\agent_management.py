"""
Agent Management API Endpoints

Purpose: Provides REST API endpoints for managing AI agents including:
- Creating, reading, updating, and deleting custom agents
- Executing individual agents on text content
- Bulk execution of multiple agents
- Agent performance analytics and usage statistics

Responsibilities:
- Agent CRUD operations via HTTP endpoints
- Agent execution request handling
- Response formatting and error handling
- Request validation and authentication
- Agent template management and instantiation

Used by: Frontend agent management UI, agent execution requests
Dependencies: core.multi_agent_engine, agents.*, core.auth
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from uuid import UUID, uuid4
from datetime import datetime
import structlog

from api.auth import get_current_user
from core.auth import UserClaims
from core.multi_agent_engine import MultiAgentEngine
from mock_data import (
    get_mock_custom_agents, 
    get_mock_agent_templates,
    get_mock_agent_execution_result,
    get_mock_agent_analytics
)

# Configure logging
logger = structlog.get_logger(__name__)

# Create router
router = APIRouter()

# Request/Response Models
class CustomAgentCreate(BaseModel):
    """Request model for creating a custom agent."""
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    type: str = Field(..., description="Agent type: grammar, style, content, etc.")
    capabilities: List[str] = Field(default_factory=list)
    style_rules: Dict[str, Any] = Field(default_factory=dict)
    priority: int = Field(default=50, ge=0, le=100)
    llm_config: Optional[Dict[str, Any]] = Field(default_factory=dict)

class CustomAgentUpdate(BaseModel):
    """Request model for updating a custom agent."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    capabilities: Optional[List[str]] = None
    style_rules: Optional[Dict[str, Any]] = None
    priority: Optional[int] = Field(None, ge=0, le=100)
    is_active: Optional[bool] = None
    llm_config: Optional[Dict[str, Any]] = None

class AgentExecutionRequest(BaseModel):
    """Request model for executing an agent."""
    text: str = Field(..., min_length=1)
    context: Optional[Dict[str, Any]] = Field(default_factory=dict)
    options: Optional[Dict[str, Any]] = Field(default_factory=dict)

class BulkAgentExecutionRequest(BaseModel):
    """Request model for executing multiple agents."""
    text: str = Field(..., min_length=1)
    agent_ids: List[UUID] = Field(..., min_items=1)
    context: Optional[Dict[str, Any]] = Field(default_factory=dict)
    options: Optional[Dict[str, Any]] = Field(default_factory=dict)

class AgentResponse(BaseModel):
    """Response model for agent data."""
    success: bool
    data: Dict[str, Any]

class AgentListResponse(BaseModel):
    """Response model for agent list."""
    success: bool
    data: List[Dict[str, Any]]
    meta: Dict[str, Any]

@router.get("/", response_model=AgentListResponse)
async def list_user_agents(
    current_user: UserClaims = Depends(get_current_user),
    limit: int = Query(default=50, le=100),
    offset: int = Query(default=0, ge=0),
    agent_type: Optional[str] = Query(default=None),
    is_active: Optional[bool] = Query(default=None)
):
    """
    List all custom agents for the authenticated user.
    
    Args:
        current_user: Current authenticated user
        limit: Maximum number of agents to return
        offset: Number of agents to skip for pagination
        agent_type: Filter by agent type
        is_active: Filter by active status
        
    Returns:
        AgentListResponse: List of user's custom agents
    """
    try:
        logger.info(
            "Listing user agents",
            user_id=current_user.user_id,
            limit=limit,
            offset=offset,
            agent_type=agent_type,
            is_active=is_active
        )
        
        # In mock mode, return mock agents
        if MultiAgentEngine.is_mock_mode():
            mock_agents = get_mock_custom_agents()
            
            # Apply filters
            filtered_agents = mock_agents
            if agent_type:
                filtered_agents = [a for a in filtered_agents if a.get("type") == agent_type]
            if is_active is not None:
                filtered_agents = [a for a in filtered_agents if a.get("is_active") == is_active]
            
            # Apply pagination
            paginated_agents = filtered_agents[offset:offset + limit]
            
            return AgentListResponse(
                success=True,
                data=paginated_agents,
                meta={
                    "total": len(filtered_agents),
                    "limit": limit,
                    "offset": offset,
                    "has_more": offset + limit < len(filtered_agents)
                }
            )
        
        # Get agents from database
        agents = await MultiAgentEngine.list_user_agents(
            user_id=current_user.user_id,
            limit=limit,
            offset=offset,
            agent_type=agent_type,
            is_active=is_active
        )
        
        return AgentListResponse(
            success=True,
            data=agents["data"],
            meta=agents["meta"]
        )
        
    except Exception as e:
        logger.error("Failed to list user agents", error=str(e), user_id=current_user.user_id)
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve agents"
        )

@router.post("/", response_model=AgentResponse)
async def create_custom_agent(
    agent_data: CustomAgentCreate,
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Create a new custom agent for the authenticated user.
    
    Args:
        agent_data: Agent creation data
        current_user: Current authenticated user
        
    Returns:
        AgentResponse: Created agent data
    """
    try:
        logger.info(
            "Creating custom agent",
            user_id=current_user.user_id,
            agent_name=agent_data.name,
            agent_type=agent_data.type
        )
        
        # In mock mode, return mock agent
        if MultiAgentEngine.is_mock_mode():
            mock_agent = {
                "id": str(uuid4()),
                "user_id": current_user.user_id,
                "name": agent_data.name,
                "description": agent_data.description,
                "type": agent_data.type,
                "capabilities": agent_data.capabilities,
                "style_rules": agent_data.style_rules,
                "priority": agent_data.priority,
                "is_active": True,
                "created_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat(),
                "usage_stats": {
                    "total_executions": 0,
                    "average_execution_time": 0.0,
                    "success_rate": 100.0,
                    "last_used": None
                }
            }
            
            return AgentResponse(
                success=True,
                data=mock_agent
            )
        
        # Create agent in database
        agent = await MultiAgentEngine.create_custom_agent(
            user_id=current_user.user_id,
            agent_data=agent_data.dict()
        )
        
        logger.info("Custom agent created successfully", agent_id=agent["id"])
        
        return AgentResponse(
            success=True,
            data=agent
        )
        
    except Exception as e:
        logger.error("Failed to create custom agent", error=str(e), user_id=current_user.user_id)
        raise HTTPException(
            status_code=500,
            detail="Failed to create agent"
        )

@router.get("/{agent_id}", response_model=AgentResponse)
async def get_agent_details(
    agent_id: UUID,
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Get details for a specific custom agent.
    
    Args:
        agent_id: Agent ID to retrieve
        current_user: Current authenticated user
        
    Returns:
        AgentResponse: Agent details
    """
    try:
        logger.info("Getting agent details", agent_id=str(agent_id), user_id=current_user.user_id)
        
        # In mock mode, return mock agent
        if MultiAgentEngine.is_mock_mode():
            mock_agents = get_mock_custom_agents()
            agent = next((a for a in mock_agents if a["id"] == str(agent_id)), None)
            
            if not agent:
                raise HTTPException(status_code=404, detail="Agent not found")
            
            return AgentResponse(
                success=True,
                data=agent
            )
        
        # Get agent from database
        agent = await MultiAgentEngine.get_agent_details(
            agent_id=str(agent_id),
            user_id=current_user.user_id
        )
        
        if not agent:
            raise HTTPException(status_code=404, detail="Agent not found")
        
        return AgentResponse(
            success=True,
            data=agent
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get agent details", error=str(e), agent_id=str(agent_id))
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve agent details"
        )

@router.put("/{agent_id}", response_model=AgentResponse)
async def update_custom_agent(
    agent_id: UUID,
    agent_updates: CustomAgentUpdate,
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Update a custom agent.
    
    Args:
        agent_id: Agent ID to update
        agent_updates: Agent update data
        current_user: Current authenticated user
        
    Returns:
        AgentResponse: Updated agent data
    """
    try:
        logger.info("Updating custom agent", agent_id=str(agent_id), user_id=current_user.user_id)
        
        # In mock mode, return updated mock agent
        if MultiAgentEngine.is_mock_mode():
            mock_agents = get_mock_custom_agents()
            agent = next((a for a in mock_agents if a["id"] == str(agent_id)), None)
            
            if not agent:
                raise HTTPException(status_code=404, detail="Agent not found")
            
            # Apply updates
            update_data = agent_updates.dict(exclude_unset=True)
            agent.update(update_data)
            agent["updated_at"] = datetime.utcnow().isoformat()
            
            return AgentResponse(
                success=True,
                data=agent
            )
        
        # Update agent in database
        agent = await MultiAgentEngine.update_custom_agent(
            agent_id=str(agent_id),
            user_id=current_user.user_id,
            updates=agent_updates.dict(exclude_unset=True)
        )
        
        if not agent:
            raise HTTPException(status_code=404, detail="Agent not found")
        
        logger.info("Custom agent updated successfully", agent_id=str(agent_id))
        
        return AgentResponse(
            success=True,
            data=agent
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to update custom agent", error=str(e), agent_id=str(agent_id))
        raise HTTPException(
            status_code=500,
            detail="Failed to update agent"
        )

@router.delete("/{agent_id}")
async def delete_custom_agent(
    agent_id: UUID,
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Delete (deactivate) a custom agent.
    
    Args:
        agent_id: Agent ID to delete
        current_user: Current authenticated user
        
    Returns:
        dict: Success response
    """
    try:
        logger.info("Deleting custom agent", agent_id=str(agent_id), user_id=current_user.user_id)
        
        # In mock mode, just return success
        if MultiAgentEngine.is_mock_mode():
            return {"success": True, "message": "Agent deleted successfully"}
        
        # Delete agent from database
        success = await MultiAgentEngine.delete_custom_agent(
            agent_id=str(agent_id),
            user_id=current_user.user_id
        )
        
        if not success:
            raise HTTPException(status_code=404, detail="Agent not found")
        
        logger.info("Custom agent deleted successfully", agent_id=str(agent_id))
        
        return {"success": True, "message": "Agent deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to delete custom agent", error=str(e), agent_id=str(agent_id))
        raise HTTPException(
            status_code=500,
            detail="Failed to delete agent"
        )

@router.post("/{agent_id}/execute", response_model=AgentResponse)
async def execute_agent(
    agent_id: UUID,
    execution_request: AgentExecutionRequest,
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Execute a specific agent on provided text.
    
    Args:
        agent_id: Agent ID to execute
        execution_request: Execution request data
        current_user: Current authenticated user
        
    Returns:
        AgentResponse: Agent execution results
    """
    try:
        logger.info(
            "Executing agent",
            agent_id=str(agent_id),
            user_id=current_user.user_id,
            text_length=len(execution_request.text)
        )
        
        # In mock mode, return mock execution result
        if MultiAgentEngine.is_mock_mode():
            return AgentResponse(
                success=True,
                data=get_mock_agent_execution_result()
            )
        
        # Execute agent
        result = await MultiAgentEngine.execute_single_agent(
            agent_id=str(agent_id),
            user_id=current_user.user_id,
            text=execution_request.text,
            context=execution_request.context,
            options=execution_request.options
        )
        
        logger.info("Agent execution completed", agent_id=str(agent_id))
        
        return AgentResponse(
            success=True,
            data=result
        )
        
    except Exception as e:
        logger.error("Agent execution failed", error=str(e), agent_id=str(agent_id))
        raise HTTPException(
            status_code=500,
            detail="Agent execution failed"
        )

@router.post("/bulk-execute", response_model=AgentResponse)
async def execute_multiple_agents(
    execution_request: BulkAgentExecutionRequest,
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Execute multiple agents on the same text.
    
    Args:
        execution_request: Bulk execution request data
        current_user: Current authenticated user
        
    Returns:
        AgentResponse: Combined execution results
    """
    try:
        logger.info(
            "Executing multiple agents",
            agent_count=len(execution_request.agent_ids),
            user_id=current_user.user_id,
            text_length=len(execution_request.text)
        )
        
        # In mock mode, return mock execution results
        if MultiAgentEngine.is_mock_mode():
            results = []
            for agent_id in execution_request.agent_ids:
                result = get_mock_agent_execution_result()
                result["agent_id"] = str(agent_id)
                results.append(result)
            
            return AgentResponse(
                success=True,
                data={
                    "results": results,
                    "execution_time": 1.5,
                    "agents_executed": len(execution_request.agent_ids)
                }
            )
        
        # Execute multiple agents
        results = await MultiAgentEngine.execute_multiple_agents(
            agent_ids=[str(aid) for aid in execution_request.agent_ids],
            user_id=current_user.user_id,
            text=execution_request.text,
            context=execution_request.context,
            options=execution_request.options
        )
        
        logger.info("Multiple agent execution completed", agent_count=len(execution_request.agent_ids))
        
        return AgentResponse(
            success=True,
            data=results
        )
        
    except Exception as e:
        logger.error("Bulk agent execution failed", error=str(e))
        raise HTTPException(
            status_code=500,
            detail="Bulk agent execution failed"
        )

@router.get("/{agent_id}/analytics", response_model=AgentResponse)
async def get_agent_analytics(
    agent_id: UUID,
    current_user: UserClaims = Depends(get_current_user),
    days: int = Query(default=30, ge=1, le=365)
):
    """
    Get performance analytics for a specific agent.
    
    Args:
        agent_id: Agent ID to get analytics for
        current_user: Current authenticated user
        days: Number of days to include in analytics
        
    Returns:
        AgentResponse: Agent performance analytics
    """
    try:
        logger.info("Getting agent analytics", agent_id=str(agent_id), days=days)
        
        # In mock mode, return mock analytics
        if MultiAgentEngine.is_mock_mode():
            return AgentResponse(
                success=True,
                data=get_mock_agent_analytics()
            )
        
        # Get analytics from database
        analytics = await MultiAgentEngine.get_agent_analytics(
            agent_id=str(agent_id),
            user_id=current_user.user_id,
            days=days
        )
        
        return AgentResponse(
            success=True,
            data=analytics
        )
        
    except Exception as e:
        logger.error("Failed to get agent analytics", error=str(e), agent_id=str(agent_id))
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve agent analytics"
        )

@router.get("/templates", response_model=AgentListResponse)
async def get_agent_templates(
    current_user: UserClaims = Depends(get_current_user),
    category: Optional[str] = Query(default=None)
):
    """
    Get available agent templates.
    
    Args:
        current_user: Current authenticated user
        category: Filter by template category
        
    Returns:
        AgentListResponse: Available agent templates
    """
    try:
        logger.info("Getting agent templates", category=category)
        
        # In mock mode, return mock templates
        if MultiAgentEngine.is_mock_mode():
            templates = get_mock_agent_templates()
            
            if category:
                templates = [t for t in templates if t.get("category") == category]
            
            return AgentListResponse(
                success=True,
                data=templates,
                meta={"total": len(templates)}
            )
        
        # Get templates from database
        templates = await MultiAgentEngine.get_agent_templates(category=category)
        
        return AgentListResponse(
            success=True,
            data=templates["data"],
            meta=templates["meta"]
        )
        
    except Exception as e:
        logger.error("Failed to get agent templates", error=str(e))
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve agent templates"
        )