import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import {
  PersonaStore,
  ReaderPersona,
  PersonaTemplate,
  PersonaFeedback,
  PersonaInsights,
  PersonaCategory,
  PersonaReaction,
  CommentType,
  FeedbackCategory,
} from '../types/persona';

// Built-in Persona Templates
const builtInTemplates: PersonaTemplate[] = [
  {
    id: 'academic-professor',
    name: 'University Professor',
    description: 'Academic with expertise in research and education',
    category: 'academic',
    isPopular: true,
    usageCount: 0,
    template: {
      category: 'academic',
      demographics: {
        age: 45,
        education: 'doctorate',
        profession: 'Professor',
      },
      readingPreferences: {
        complexity: 'complex',
        attentionSpan: 'long',
        preferredLength: 'detailed',
      },
      personality: {
        traits: ['analytical', 'critical', 'methodical'],
        criticalThinking: 'high',
        emotionalSensitivity: 'medium',
      },
      context: {
        purpose: 'evaluation',
        expertise: 'expert',
        relationship: 'colleague',
      },
      feedbackStyle: {
        tone: 'constructive',
        focus: 'technical',
        detail: 'specific',
        priorities: ['accuracy', 'methodology', 'citations'],
      },
      color: 'blue',
    },
  },
  {
    id: 'professional-ceo',
    name: 'CEO/Executive',
    description: 'Busy executive focused on results and efficiency',
    category: 'professional',
    isPopular: true,
    usageCount: 0,
    template: {
      category: 'professional',
      demographics: {
        age: 50,
        education: 'masters',
        profession: 'CEO',
      },
      readingPreferences: {
        readingSpeed: 'fast',
        attentionSpan: 'short',
        preferredLength: 'brief',
        complexity: 'moderate',
      },
      personality: {
        traits: ['decisive', 'results-oriented', 'time-conscious'],
        criticalThinking: 'high',
        emotionalSensitivity: 'low',
      },
      context: {
        purpose: 'work',
        expertise: 'specialist',
        relationship: 'colleague',
        timeConstraints: 'urgent',
      },
      feedbackStyle: {
        tone: 'harsh',
        focus: 'content',
        detail: 'high-level',
        priorities: ['clarity', 'impact', 'brevity'],
      },
      color: 'purple',
    },
  },
  {
    id: 'creative-agent',
    name: 'Literary Agent',
    description: 'Publishing professional evaluating commercial potential',
    category: 'creative',
    isPopular: true,
    usageCount: 0,
    template: {
      category: 'creative',
      demographics: {
        age: 35,
        education: 'bachelors',
        profession: 'Literary Agent',
      },
      readingPreferences: {
        genres: ['fiction', 'literary', 'commercial'],
        readingSpeed: 'fast',
        attentionSpan: 'medium',
        complexity: 'moderate',
      },
      personality: {
        traits: ['commercial-minded', 'trend-aware', 'audience-focused'],
        criticalThinking: 'high',
        emotionalSensitivity: 'medium',
      },
      context: {
        purpose: 'evaluation',
        expertise: 'expert',
        relationship: 'stranger',
      },
      feedbackStyle: {
        tone: 'constructive',
        focus: 'structure',
        detail: 'specific',
        priorities: ['hook', 'marketability', 'pacing'],
      },
      color: 'green',
    },
  },
  {
    id: 'personal-friend',
    name: 'Close Friend',
    description: 'Supportive friend providing honest feedback',
    category: 'personal',
    isPopular: true,
    usageCount: 0,
    template: {
      category: 'personal',
      demographics: {
        age: 30,
        education: 'bachelors',
      },
      readingPreferences: {
        readingSpeed: 'average',
        attentionSpan: 'medium',
        complexity: 'simple',
      },
      personality: {
        traits: ['supportive', 'honest', 'empathetic'],
        criticalThinking: 'medium',
        emotionalSensitivity: 'high',
      },
      context: {
        purpose: 'personal',
        expertise: 'novice',
        relationship: 'friend',
      },
      feedbackStyle: {
        tone: 'encouraging',
        focus: 'emotion',
        detail: 'high-level',
        priorities: ['clarity', 'relatability', 'engagement'],
      },
      color: 'pink',
    },
  },
  {
    id: 'teen-reader',
    name: 'Teen Reader',
    description: 'Young adult reader with modern preferences',
    category: 'age-specific',
    isPopular: true,
    usageCount: 0,
    template: {
      category: 'age-specific',
      demographics: {
        age: 16,
        education: 'high-school',
      },
      readingPreferences: {
        genres: ['young-adult', 'fantasy', 'romance'],
        readingSpeed: 'fast',
        attentionSpan: 'short',
        complexity: 'simple',
      },
      personality: {
        traits: ['trend-conscious', 'social', 'emotional'],
        criticalThinking: 'medium',
        emotionalSensitivity: 'high',
      },
      context: {
        purpose: 'entertainment',
        expertise: 'novice',
        relationship: 'stranger',
      },
      feedbackStyle: {
        tone: 'gentle',
        focus: 'emotion',
        detail: 'high-level',
        priorities: ['relatability', 'diversity', 'authenticity'],
      },
      color: 'orange',
    },
  },
];

// Built-in Personas created from templates
const builtInPersonas: ReaderPersona[] = builtInTemplates.map(template => ({
  id: template.id,
  name: template.name,
  description: template.description,
  category: template.category,
  demographics: template.template.demographics || {},
  readingPreferences: template.template.readingPreferences || {},
  personality: template.template.personality || {},
  context: template.template.context || {},
  feedbackStyle: template.template.feedbackStyle || {},
  isBuiltIn: true,
  isActive: false,
  tags: [template.category, 'built-in'],
  color: template.template.color || 'gray',
  createdAt: new Date(),
  updatedAt: new Date(),
}));

// Mock feedback generator (would be replaced with actual AI service)
const generateMockFeedback = async (
  documentId: string,
  content: string,
  personas: ReaderPersona[]
): Promise<PersonaFeedback[]> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1500));
  
  const wordCount = content.trim().split(/\s+/).filter(Boolean).length;
  const sentences = content.split(/[.!?]+/).filter(Boolean).length;
  const avgWordsPerSentence = sentences > 0 ? wordCount / sentences : 0;
  
  return personas.map(persona => {
    // Generate feedback based on persona characteristics
    const engagementScore = Math.floor(Math.random() * 40) + 60; // 60-100
    const comprehensionScore = persona.readingPreferences.complexity === 'complex' 
      ? Math.floor(Math.random() * 30) + 70
      : Math.floor(Math.random() * 20) + 80;
    
    const reactions: PersonaReaction[] = ['love', 'like', 'neutral', 'confused', 'bored'];
    const overallReaction = reactions[Math.floor(Math.random() * reactions.length)];
    
    const feedback: PersonaFeedback = {
      personaId: persona.id,
      documentId,
      overallReaction,
      engagementScore,
      comprehensionScore,
      feedback: [
        {
          id: `feedback-${Date.now()}-${Math.random()}`,
          type: 'suggestion',
          severity: 'medium',
          title: `${persona.name}'s feedback`,
          message: generatePersonaSpecificFeedback(persona, content, avgWordsPerSentence),
          category: 'engagement',
          tags: [persona.category],
          lineNumber: Math.floor(Math.random() * 10) + 1,
        },
      ],
      predictions: {
        willFinishReading: engagementScore,
        wouldRecommend: Math.floor(Math.random() * 30) + 70,
        emotionalImpact: 'medium',
        memorability: 'memorable',
      },
      confidence: Math.floor(Math.random() * 20) + 80,
      generatedAt: new Date(),
    };
    
    return feedback;
  });
};

const generatePersonaSpecificFeedback = (
  persona: ReaderPersona,
  content: string,
  avgWordsPerSentence: number
): string => {
  const wordCount = content.trim().split(/\s+/).filter(Boolean).length;
  
  if (persona.category === 'academic') {
    return `As an academic reviewer, I notice this text needs more supporting evidence and citations. The argument structure could be strengthened with clearer thesis statements.`;
  } else if (persona.category === 'professional') {
    if (wordCount > 500) {
      return `This is too long for executive consumption. Get to the key points faster and use bullet points for better scanning.`;
    }
    return `Good professional tone, but consider adding more concrete data and actionable recommendations.`;
  } else if (persona.category === 'creative') {
    return `The narrative voice is engaging, but the pacing feels uneven. Consider adding more dialogue to break up exposition.`;
  } else if (persona.category === 'personal') {
    return `I love the personal touch in your writing! It feels authentic and relatable. Maybe add a few more specific examples.`;
  } else if (persona.category === 'age-specific' && persona.demographics.age && persona.demographics.age < 20) {
    return `This speaks to me! The language feels natural and the topics are relevant. Maybe add some social media references?`;
  }
  
  return `This is well-written overall. Consider adjusting the complexity level to better match your target audience.`;
};

export const usePersonaStore = create<PersonaStore>()(
  persist(
    (set, get) => ({
      // State
      personas: builtInPersonas,
      selectedPersonas: [],
      templates: builtInTemplates,
      feedback: [],
      insights: null,
      activeFeedbackMode: 'onDemand',
      maxConcurrentPersonas: 5,
      showConsensusOnly: false,
      lastUpdated: new Date(),

      // Persona Management
      createPersona: (personaData) => {
        const newPersona: ReaderPersona = {
          ...personaData,
          id: `persona-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          isBuiltIn: false,
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        set((state) => ({
          personas: [...state.personas, newPersona],
          lastUpdated: new Date(),
        }));
        
        return newPersona;
      },

      updatePersona: (id, updates) => {
        set((state) => ({
          personas: state.personas.map((persona) =>
            persona.id === id
              ? { ...persona, ...updates, updatedAt: new Date() }
              : persona
          ),
          lastUpdated: new Date(),
        }));
      },

      deletePersona: (id) => {
        set((state) => ({
          personas: state.personas.filter((persona) => persona.id !== id && !persona.isBuiltIn),
          selectedPersonas: state.selectedPersonas.filter((personaId) => personaId !== id),
          lastUpdated: new Date(),
        }));
      },

      duplicatePersona: (id) => {
        const persona = get().personas.find((p) => p.id === id);
        if (persona) {
          const duplicated: ReaderPersona = {
            ...persona,
            id: `persona-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            name: `${persona.name} (Copy)`,
            isBuiltIn: false,
            createdAt: new Date(),
            updatedAt: new Date(),
          };

          set((state) => ({
            personas: [...state.personas, duplicated],
            lastUpdated: new Date(),
          }));
        }
      },

      togglePersonaActive: (id) => {
        set((state) => ({
          personas: state.personas.map((persona) =>
            persona.id === id ? { ...persona, isActive: !persona.isActive } : persona
          ),
          lastUpdated: new Date(),
        }));
      },

      // Selection
      selectPersona: (id) => {
        set((state) => {
          const maxPersonas = state.maxConcurrentPersonas;
          const selected = state.selectedPersonas;
          
          if (selected.includes(id)) return state;
          
          const newSelected = selected.length >= maxPersonas
            ? [...selected.slice(1), id] // Remove oldest, add new
            : [...selected, id];
          
          return {
            selectedPersonas: newSelected,
            lastUpdated: new Date(),
          };
        });
      },

      toggleSelectedPersona: (id) => {
        set((state) => {
          const selected = state.selectedPersonas;
          const isSelected = selected.includes(id);
          
          const newSelected = isSelected
            ? selected.filter(personaId => personaId !== id)
            : selected.length >= state.maxConcurrentPersonas
              ? [...selected.slice(1), id] // Remove oldest, add new
              : [...selected, id];
          
          return {
            selectedPersonas: newSelected,
            lastUpdated: new Date(),
          };
        });
      },

      deselectPersona: (id) => {
        set((state) => ({
          selectedPersonas: state.selectedPersonas.filter((personaId) => personaId !== id),
          lastUpdated: new Date(),
        }));
      },

      selectAllPersonas: () => {
        set((state) => {
          const activePersonas = state.personas
            .filter(persona => persona.isActive)
            .slice(0, state.maxConcurrentPersonas)
            .map(persona => persona.id);
          
          return {
            selectedPersonas: activePersonas,
            lastUpdated: new Date(),
          };
        });
      },

      deselectAllPersonas: () => {
        set({
          selectedPersonas: [],
          lastUpdated: new Date(),
        });
      },

      // Templates
      createTemplate: (templateData) => {
        const newTemplate: PersonaTemplate = {
          ...templateData,
          id: `template-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          usageCount: 0,
        };

        set((state) => ({
          templates: [...state.templates, newTemplate],
          lastUpdated: new Date(),
        }));
      },

      useTemplate: (templateId, customizations = {}) => {
        const template = get().templates.find(t => t.id === templateId);
        if (!template) return;

        const newPersona: ReaderPersona = {
          id: `persona-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          name: customizations.name || template.name,
          description: customizations.description || template.description,
          category: template.category,
          demographics: { ...template.template.demographics, ...customizations.demographics },
          readingPreferences: { ...template.template.readingPreferences, ...customizations.readingPreferences },
          personality: { ...template.template.personality, ...customizations.personality },
          context: { ...template.template.context, ...customizations.context },
          feedbackStyle: { ...template.template.feedbackStyle, ...customizations.feedbackStyle },
          isBuiltIn: false,
          isActive: true,
          tags: customizations.tags || [template.category],
          color: customizations.color || template.template.color || 'gray',
          createdAt: new Date(),
          updatedAt: new Date(),
          ...customizations,
        };

        // Increment template usage
        set((state) => ({
          personas: [...state.personas, newPersona],
          templates: state.templates.map(t => 
            t.id === templateId ? { ...t, usageCount: t.usageCount + 1 } : t
          ),
          lastUpdated: new Date(),
        }));
      },

      // Feedback Generation
      generateFeedback: async (documentId, content, selectedPersonas) => {
        const state = get();
        const personaIds = selectedPersonas || state.selectedPersonas;
        const personas = state.personas.filter(p => personaIds.includes(p.id));
        
        if (personas.length === 0) return [];
        
        const feedback = await generateMockFeedback(documentId, content, personas);
        
        set((state) => ({
          feedback: [
            ...state.feedback.filter(f => f.documentId !== documentId),
            ...feedback,
          ],
          lastUpdated: new Date(),
        }));
        
        return feedback;
      },

      generateInsights: async (documentId) => {
        const state = get();
        const documentFeedback = state.feedback.filter(f => f.documentId === documentId);
        
        if (documentFeedback.length === 0) return null;
        
        // Generate mock insights
        const insights: PersonaInsights = {
          documentId,
          activePersonas: documentFeedback.map(f => f.personaId),
          consensus: {
            agreements: [],
            disagreements: [],
            universalIssues: [],
            polarizingElements: [],
          },
          audienceCompatibility: {
            primaryAudience: {
              personaId: documentFeedback[0].personaId,
              compatibilityScore: Math.floor(Math.random() * 30) + 70,
            },
            crossAudienceAppeal: Math.floor(Math.random() * 40) + 60,
            audienceConflicts: [],
          },
          optimizations: [],
          generatedAt: new Date(),
        };
        
        set((state) => ({
          insights,
          lastUpdated: new Date(),
        }));
        
        return insights;
      },

      clearFeedback: (documentId) => {
        set((state) => ({
          feedback: documentId 
            ? state.feedback.filter(f => f.documentId !== documentId)
            : [],
          insights: documentId && state.insights?.documentId === documentId ? null : state.insights,
          lastUpdated: new Date(),
        }));
      },

      // Import/Export
      exportPersonas: (personaIds) => {
        const state = get();
        const personasToExport = personaIds
          ? state.personas.filter((persona) => personaIds.includes(persona.id))
          : state.personas.filter((persona) => !persona.isBuiltIn);

        return JSON.stringify(personasToExport, null, 2);
      },

      importPersonas: (data) => {
        try {
          const importedPersonas: ReaderPersona[] = JSON.parse(data);
          
          set((state) => ({
            personas: [
              ...state.personas,
              ...importedPersonas.map((persona) => ({
                ...persona,
                id: `persona-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                isBuiltIn: false,
                createdAt: new Date(),
                updatedAt: new Date(),
              })),
            ],
            lastUpdated: new Date(),
          }));
        } catch (error) {
          console.error('Failed to import personas:', error);
        }
      },

      // Settings
      updateSettings: (settings) => {
        set((state) => ({
          ...state,
          ...settings,
          lastUpdated: new Date(),
        }));
      },

      // Getters
      getPersona: (id) => {
        return get().personas.find((persona) => persona.id === id);
      },

      getActivePersonas: () => {
        return get().personas.filter((persona) => persona.isActive);
      },

      getPersonasByCategory: (category) => {
        return get().personas.filter((persona) => persona.category === category);
      },

      getFeedbackForDocument: (documentId) => {
        return get().feedback.filter((feedback) => feedback.documentId === documentId);
      },
    }),
    {
      name: 'persona-store',
      version: 1,
    }
  )
);

// Computed selectors
export const usePersonaSelectors = () => {
  const store = usePersonaStore();
  
  return {
    // Get selected personas with full data
    getSelectedPersonas: () => {
      return store.personas.filter(p => store.selectedPersonas.includes(p.id));
    },
    
    // Get feedback summary for document
    getFeedbackSummary: (documentId: string) => {
      const feedback = store.getFeedbackForDocument(documentId);
      return {
        totalFeedback: feedback.length,
        averageEngagement: feedback.reduce((acc, f) => acc + f.engagementScore, 0) / (feedback.length || 1),
        averageComprehension: feedback.reduce((acc, f) => acc + f.comprehensionScore, 0) / (feedback.length || 1),
        reactions: feedback.map(f => f.overallReaction),
      };
    },
    
    // Get personas by category with counts
    getCategoryCounts: () => {
      const categories: Record<PersonaCategory, number> = {
        academic: 0,
        professional: 0,
        creative: 0,
        personal: 0,
        technical: 0,
        cultural: 0,
        'age-specific': 0,
        custom: 0,
      };
      
      store.personas.forEach(persona => {
        categories[persona.category]++;
      });
      
      return categories;
    },
    
    // Check if can select more personas
    canSelectMore: () => {
      return store.selectedPersonas.length < store.maxConcurrentPersonas;
    },
  };
};