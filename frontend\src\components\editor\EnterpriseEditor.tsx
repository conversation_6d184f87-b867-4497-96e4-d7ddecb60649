import React, { useState, useCallback, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import WritingHealthBadge from './WritingHealthBadge';
import WritingHealthPanel from './WritingHealthPanel';
import EnhancedAIPanel from './EnhancedAIPanel';
import SmartCitationSearch from './SmartCitationSearch';
import CitationManager from './CitationManager';
import VersionControl from './VersionControl';
import ImportWizard from './ImportWizard';
import LivePresence from './LivePresence';
import StatusBar from './StatusBar';
import { WritingHealthAnalyzer, WritingHealthScore, HealthIssue } from '../../workers/WritingHealthWorker';

// Mock data interfaces
interface Citation {
  id: string;
  title: string;
  authors: string[];
  year: number;
  source: string;
  type: 'journal' | 'book' | 'website' | 'report' | 'article';
  doi?: string;
  url?: string;
  abstract?: string;
  tags?: string[];
  style: 'apa' | 'mla' | 'chicago';
  usageCount: number;
  dateAdded: Date;
}

interface AISuggestion {
  id: string;
  type: 'grammar' | 'style' | 'content' | 'structure';
  severity: 'low' | 'medium' | 'high';
  title: string;
  description: string;
  originalText: string;
  suggestedText: string;
  line: number;
  isStale?: boolean;
}

interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  color: string;
  isOnline: boolean;
  lastSeen?: Date;
}

interface UserPresence extends User {
  status: 'active' | 'idle' | 'away' | 'offline';
  cursor?: {
    x: number;
    y: number;
    selection?: { start: number; end: number };
  };
  currentAction?: 'typing' | 'selecting' | 'editing' | 'viewing';
  location?: { line: number; character: number };
}

interface Checkpoint {
  id: string;
  timestamp: Date;
  message?: string;
  wordCount: number;
  author: string;
  isAutoSave: boolean;
  changes?: { added: number; removed: number };
}

interface DraftVersion {
  id: string;
  name: string;
  description?: string;
  createdAt: Date;
  lastModified: Date;
  wordCount: number;
  author: string;
  isActive: boolean;
  checkpoints: Checkpoint[];
  parentVersionId?: string;
}

interface EnterpriseEditorProps {
  content: string;
  onContentChange: (content: string) => void;
  isAutoSaving: boolean;
  lastSaved: Date | null;
  documentId: string;
  userId: string;
}

const EnterpriseEditor: React.FC<EnterpriseEditorProps> = ({
  content,
  onContentChange,
  isAutoSaving,
  lastSaved,
  documentId,
  userId,
}) => {
  // Writing Health State
  const [healthScore, setHealthScore] = useState<WritingHealthScore>({
    overall: 78,
    readingLevel: 82,
    wordiness: 75,
    passiveVoice: 88,
    inclusiveLanguage: 95,
    brandVoice: 70,
  });
  const [healthIssues, setHealthIssues] = useState<HealthIssue[]>([]);
  const [isHealthPanelOpen, setIsHealthPanelOpen] = useState(false);

  // AI Panel State
  const [aiSuggestions, setAiSuggestions] = useState<AISuggestion[]>([]);
  const [isAIGenerating, setIsAIGenerating] = useState(false);
  const [activeAITab, setActiveAITab] = useState<'ai' | 'outline' | 'comments'>('ai');
  const [selectedText, setSelectedText] = useState<string>('');

  // Citation State
  const [isCitationSearchOpen, setIsCitationSearchOpen] = useState(false);
  const [isCitationManagerOpen, setIsCitationManagerOpen] = useState(false);
  const [citations, setCitations] = useState<Citation[]>([]);

  // Version Control State
  const [isVersionControlOpen, setIsVersionControlOpen] = useState(false);
  const [currentVersion, setCurrentVersion] = useState<DraftVersion>({
    id: 'main',
    name: 'Main Draft',
    description: 'Primary working version',
    createdAt: new Date(Date.now() - 86400000),
    lastModified: new Date(),
    wordCount: content.split(/\s+/).filter(Boolean).length,
    author: 'Current User',
    isActive: true,
    checkpoints: [],
  });
  const [versions, setVersions] = useState<DraftVersion[]>([currentVersion]);

  // Import State
  const [isImportWizardOpen, setIsImportWizardOpen] = useState(false);

  // Collaboration State
  const [currentUser] = useState<User>({
    id: userId,
    name: 'Current User',
    email: '<EMAIL>',
    color: '#3b82f6',
    isOnline: true,
  });
  const [collaborators, setCollaborators] = useState<UserPresence[]>([
    {
      id: 'user1',
      name: 'Sarah Wilson',
      email: '<EMAIL>',
      color: '#10b981',
      isOnline: true,
      status: 'active',
      currentAction: 'typing',
      location: { line: 15, character: 25 },
      cursor: { x: 400, y: 300 },
    },
    {
      id: 'user2',
      name: 'Mike Johnson',
      email: '<EMAIL>',
      color: '#f59e0b',
      isOnline: true,
      status: 'active',
      currentAction: 'viewing',
      location: { line: 8, character: 12 },
    },
  ]);
  const [connectionStatus] = useState<'connected' | 'connecting' | 'disconnected'>('connected');

  // UI State
  const [focusMode, setFocusMode] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [readingTime, setReadingTime] = useState(0);

  // Initialize Writing Health Analyzer
  const analyzer = new WritingHealthAnalyzer();

  // Analyze content for health metrics
  useEffect(() => {
    const analyzeContent = async () => {
      if (content.trim()) {
        const result = analyzer.analyzeText(content);
        setHealthScore(result.score);
        setHealthIssues(result.issues);
        setReadingTime(Math.ceil(result.wordCount / 200)); // 200 words per minute
      }
    };

    const timeoutId = setTimeout(analyzeContent, 500); // Debounce
    return () => clearTimeout(timeoutId);
  }, [content]);

  // Handle Writing Health
  const handleFixHealthIssue = useCallback((issueId: string) => {
    console.log('Fixing health issue:', issueId);
    // In real implementation, this would trigger AI to fix the issue
  }, []);

  const handleNavigateToLine = useCallback((line: number) => {
    console.log('Navigating to line:', line);
    // In real implementation, this would scroll and highlight the line
  }, []);

  // Handle AI Suggestions
  const handleAcceptSuggestion = useCallback((id: string) => {
    const suggestion = aiSuggestions.find(s => s.id === id);
    if (suggestion) {
      // Apply the suggestion to content
      const newContent = content.replace(suggestion.originalText, suggestion.suggestedText);
      onContentChange(newContent);
      setAiSuggestions(prev => prev.filter(s => s.id !== id));
    }
  }, [aiSuggestions, content, onContentChange]);

  const handleDismissSuggestion = useCallback((id: string) => {
    setAiSuggestions(prev => prev.filter(s => s.id !== id));
  }, []);

  const handleRerunSuggestion = useCallback((id: string) => {
    console.log('Rerunning suggestion:', id);
    // In real implementation, this would regenerate the suggestion
  }, []);

  const handleGenerateContent = useCallback((mode: 'fix-its' | 'rephrase' | 'build-from' | 'analyze', options?: any) => {
    setIsAIGenerating(true);
    console.log('Generating content:', mode, options);
    
    // Simulate AI generation
    setTimeout(() => {
      if (mode === 'fix-its') {
        // Add mock suggestions
        const mockSuggestions: AISuggestion[] = [
          {
            id: `suggestion-${Date.now()}`,
            type: 'grammar',
            severity: 'medium',
            title: 'Grammar Check',
            description: 'Subject-verb agreement issue',
            originalText: 'The team are working',
            suggestedText: 'The team is working',
            line: 5,
          },
        ];
        setAiSuggestions(prev => [...prev, ...mockSuggestions]);
      }
      setIsAIGenerating(false);
    }, 2000);
  }, []);

  // Handle Citations
  const handleInsertCitation = useCallback((citation: Citation) => {
    const citationText = `[${citation.authors[0].split(',')[0]} et al., ${citation.year}]`;
    onContentChange(content + citationText);
    setCitations(prev => {
      const updated = prev.map(c => 
        c.id === citation.id ? { ...c, usageCount: c.usageCount + 1 } : c
      );
      if (!updated.find(c => c.id === citation.id)) {
        updated.push({ ...citation, usageCount: 1, dateAdded: new Date() });
      }
      return updated;
    });
  }, [content, onContentChange]);

  // Handle Version Control
  const handleCreateVersion = useCallback((name: string, description?: string) => {
    const newVersion: DraftVersion = {
      id: `version-${Date.now()}`,
      name,
      description,
      createdAt: new Date(),
      lastModified: new Date(),
      wordCount: content.split(/\s+/).filter(Boolean).length,
      author: currentUser.name,
      isActive: false,
      checkpoints: [],
    };
    setVersions(prev => [...prev, newVersion]);
  }, [content, currentUser.name]);

  const handleSwitchVersion = useCallback((versionId: string) => {
    setVersions(prev => prev.map(v => ({ ...v, isActive: v.id === versionId })));
    setCurrentVersion(versions.find(v => v.id === versionId) || currentVersion);
  }, [versions, currentVersion]);

  // Handle Import
  const handleImportComplete = useCallback((files: any[], options: any) => {
    console.log('Import completed:', files, options);
    // In real implementation, this would process and merge the imported content
  }, []);

  return (
    <div className="h-screen flex flex-col bg-slate-50">
      {/* Header with Live Presence */}
      <div className="flex items-center justify-between px-6 py-3 bg-white border-b border-slate-200">
        <div className="flex items-center space-x-4">
          <h1 className="text-lg font-semibold text-slate-900">Enterprise Editor</h1>
          <LivePresence
            currentUser={currentUser}
            users={collaborators}
            connectionStatus={connectionStatus}
            onUserClick={(user) => console.log('User clicked:', user)}
          />
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setIsImportWizardOpen(true)}
            className="px-3 py-1.5 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Import
          </button>
          <button
            onClick={() => setIsCitationManagerOpen(true)}
            className="px-3 py-1.5 text-sm bg-slate-200 text-slate-700 rounded-lg hover:bg-slate-300 transition-colors"
          >
            Citations
          </button>
          <button
            onClick={() => setIsVersionControlOpen(true)}
            className="px-3 py-1.5 text-sm bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            Versions
          </button>
        </div>
      </div>

      {/* Main Editor Area */}
      <div className="flex-1 flex">
        {/* Editor Content */}
        <div className="flex-1 flex flex-col">
          <div className="flex-1 p-6">
            <textarea
              value={content}
              onChange={(e) => onContentChange(e.target.value)}
              onSelect={(e) => {
                const target = e.target as HTMLTextAreaElement;
                const selected = target.value.substring(target.selectionStart, target.selectionEnd);
                setSelectedText(selected);
              }}
              placeholder="Start writing something amazing..."
              className="w-full h-full resize-none border-none outline-none text-lg leading-relaxed bg-transparent"
            />
          </div>

          {/* Status Bar */}
          <StatusBar
            content={content}
            isAutoSaving={isAutoSaving}
            lastSaved={lastSaved}
            readingTime={readingTime}
            focusMode={focusMode}
            onToggleFocusMode={() => setFocusMode(!focusMode)}
            writingHealthScore={healthScore.overall}
          />
        </div>

        {/* AI Panel */}
        <AnimatePresence>
          {!isCollapsed && (
            <motion.div
              initial={{ width: 0, opacity: 0 }}
              animate={{ width: 384, opacity: 1 }}
              exit={{ width: 0, opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              <EnhancedAIPanel
                suggestions={aiSuggestions}
                onAcceptSuggestion={handleAcceptSuggestion}
                onDismissSuggestion={handleDismissSuggestion}
                onRerunSuggestion={handleRerunSuggestion}
                onGenerateContent={handleGenerateContent}
                isGenerating={isAIGenerating}
                documentContent={content}
                selectedText={selectedText}
                onNavigateToLine={handleNavigateToLine}
                activeTab={activeAITab}
                onTabChange={setActiveAITab}
                isCollapsed={isCollapsed}
                onToggleCollapse={() => setIsCollapsed(!isCollapsed)}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Writing Health Badge */}
      <WritingHealthBadge
        score={healthScore}
        onOpenPanel={() => setIsHealthPanelOpen(true)}
        isVisible={!focusMode}
      />

      {/* Modals and Panels */}
      <WritingHealthPanel
        isOpen={isHealthPanelOpen}
        onClose={() => setIsHealthPanelOpen(false)}
        score={healthScore}
        issues={healthIssues}
        onFixIssue={handleFixHealthIssue}
        onNavigateToLine={handleNavigateToLine}
      />

      <SmartCitationSearch
        isOpen={isCitationSearchOpen}
        onClose={() => setIsCitationSearchOpen(false)}
        onInsertCitation={handleInsertCitation}
      />

      <CitationManager
        isOpen={isCitationManagerOpen}
        onClose={() => setIsCitationManagerOpen(false)}
        citations={citations}
        onAddCitation={() => setIsCitationSearchOpen(true)}
        onEditCitation={(citation) => console.log('Edit citation:', citation)}
        onDeleteCitation={(id) => setCitations(prev => prev.filter(c => c.id !== id))}
        onInsertCitation={handleInsertCitation}
        onExportBibliography={(style, citations) => console.log('Export:', style, citations)}
      />

      <VersionControl
        isOpen={isVersionControlOpen}
        onClose={() => setIsVersionControlOpen(false)}
        currentVersion={currentVersion}
        versions={versions}
        onCreateVersion={handleCreateVersion}
        onSwitchVersion={handleSwitchVersion}
        onMergeVersions={(sourceId, targetId) => console.log('Merge:', sourceId, targetId)}
        onRestoreCheckpoint={(checkpointId) => console.log('Restore:', checkpointId)}
        onCreateCheckpoint={(message) => console.log('Create checkpoint:', message)}
        onPreviewCheckpoint={(checkpointId) => console.log('Preview:', checkpointId)}
      />

      <ImportWizard
        isOpen={isImportWizardOpen}
        onClose={() => setIsImportWizardOpen(false)}
        onImportComplete={handleImportComplete}
      />

      {/* Keyboard Shortcuts Handler */}
      <div className="hidden">
        {/* Citation Search: @@ */}
        <button onClick={() => setIsCitationSearchOpen(true)}>@@</button>
        
        {/* Command Palette: Cmd+K */}
        <button onClick={() => console.log('Command palette')}>⌘K</button>
        
        {/* Version History: Cmd+Shift+H */}
        <button onClick={() => setIsVersionControlOpen(true)}>⌘⇧H</button>
      </div>
    </div>
  );
};

export default EnterpriseEditor;