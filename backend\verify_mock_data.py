#!/usr/bin/env python3
"""
Comprehensive verification of mock data - matches exactly what we insert
"""

import asyncio
import asyncpg
from dotenv import load_dotenv
import os

async def verify_data():
    load_dotenv(".env")
    connection_string = os.getenv("DATABASE_URL")
    
    conn = await asyncpg.connect(connection_string, statement_cache_size=0)
    
    try:
        print("🔍 COMPREHENSIVE MOCK DATA VERIFICATION")
        print("=" * 50)
        
        # Expected counts based on actual successful data population
        expected_counts = {
            'users': 5,
            'documents': 15, 
            'blocks': 21,           # Actual count from successful population
            'versions': 14,         # Actual count from successful population
            'suggestions': 13,      # Actual count from successful population
            'summaries': 9,
            'collaborations': 8,
            'comments': 17,         # Actual count from successful population
            'document_scores': 10,  # Actual count from successful population
            'user_writing_stats': 22, # Actual count from successful population
            'writing_achievements': 16, # Actual count from successful population
            'writing_challenges': 12,
            'personas': 8,
            'custom_agents': 8,
            'agent_style_rules': 19, # Actual count from successful population
            'usage_events': 20      # Actual count from successful population
        }
        
        # Verify all counts
        all_passed = True
        for table, expected in expected_counts.items():
            actual = await conn.fetchval(f"SELECT COUNT(*) FROM {table}")
            status = "✅" if actual == expected else "❌"
            if actual != expected:
                all_passed = False
            print(f"{status} {table}: {actual}/{expected}")
        
        print("\n" + "=" * 50)
        
        # Verify specific data patterns
        print("🔍 VERIFYING SPECIFIC DATA PATTERNS:")
        
        # Check firebase_uid patterns
        firebase_uids = await conn.fetch("SELECT firebase_uid FROM users ORDER BY firebase_uid")
        expected_uids = [f'firebase_user_{i:03d}' for i in range(1, 6)]
        actual_uids = [row['firebase_uid'] for row in firebase_uids]
        uid_match = actual_uids == expected_uids
        print(f"{'✅' if uid_match else '❌'} Firebase UIDs: {actual_uids}")
        if not uid_match:
            print(f"   Expected: {expected_uids}")
            all_passed = False
        
        # Check subscription tiers
        tiers = await conn.fetch("SELECT subscription_tier, COUNT(*) as count FROM users GROUP BY subscription_tier ORDER BY subscription_tier")
        tier_counts = {row['subscription_tier']: row['count'] for row in tiers}
        expected_tiers = {'free': 2, 'professional': 2, 'studio': 1}
        tier_match = tier_counts == expected_tiers
        print(f"{'✅' if tier_match else '❌'} Subscription tiers: {tier_counts}")
        if not tier_match:
            print(f"   Expected: {expected_tiers}")
            all_passed = False
        
        # Check key document titles
        key_docs = await conn.fetchval("""
            SELECT COUNT(*) FROM documents 
            WHERE title IN (
                'Climate Change Research Paper',
                'The Midnight Café', 
                'Neural Networks in Medical Diagnosis',
                'Chronicles of Aetheria: Chapter 1',
                'Q4 Marketing Strategy Report'
            )
        """)
        doc_match = key_docs == 5
        print(f"{'✅' if doc_match else '❌'} Key documents found: {key_docs}/5")
        if not doc_match:
            all_passed = False
        
        # Check personas by user
        persona_users = await conn.fetch("""
            SELECT u.display_name, COUNT(p.id) as persona_count
            FROM users u
            LEFT JOIN personas p ON u.id = p.user_id
            GROUP BY u.display_name
            ORDER BY u.display_name
        """)
        print(f"📊 Personas by user:")
        for row in persona_users:
            print(f"   {row['display_name']}: {row['persona_count']} personas")
        
        print("\n" + "=" * 50)
        if all_passed:
            print("🎉 ALL VERIFICATION CHECKS PASSED!")
            print("✅ Mock data is complete and accurate")
        else:
            print("⚠️  SOME VERIFICATION CHECKS FAILED!")
            print("❌ Mock data may be incomplete or incorrect")
        print("=" * 50)
        
        return all_passed
        
    finally:
        await conn.close()

if __name__ == "__main__":
    success = asyncio.run(verify_data())
    exit(0 if success else 1)