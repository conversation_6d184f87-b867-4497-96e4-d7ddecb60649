#!/usr/bin/env python3
"""
Comprehensive Supabase Database Schema Analysis
Shows tables, columns, constraints, row counts, and identifies potential gaps
"""

import asyncio
import asyncpg
from dotenv import load_dotenv
import os
from typing import Dict, List, Set

async def check_database_schema():
    print("=" * 80)
    print("   COMPREHENSIVE SUPABASE DATABASE SCHEMA ANALYSIS")
    print("=" * 80)
    
    load_dotenv(".env")
    connection_string = os.getenv("DATABASE_URL")
    
    try:
        conn = await asyncpg.connect(connection_string, statement_cache_size=0)
        print("✅ Connected to database successfully")
        print()
        
        # 1. Database info
        version = await conn.fetchval("SELECT version()")
        current_db = await conn.fetchval("SELECT current_database()")
        print(f"🗄️  Database: {version.split(',')[0]}")
        print(f"📊 Current Database: {current_db}")
        print()
        
        # 2. Extensions check
        print("🔌 INSTALLED EXTENSIONS:")
        print("-" * 40)
        extensions = await conn.fetch("SELECT extname, extversion FROM pg_extension ORDER BY extname")
        for ext in extensions:
            print(f"   ✅ {ext['extname']} (v{ext['extversion']})")
        
        required_extensions = ['uuid-ossp', 'ltree', 'vector', 'pg_trgm']
        missing_extensions = []
        for ext_name in required_extensions:
            exists = await conn.fetchval("SELECT EXISTS (SELECT FROM pg_extension WHERE extname = $1)", ext_name)
            if not exists:
                missing_extensions.append(ext_name)
        
        if missing_extensions:
            print(f"\n   ❌ Missing required extensions: {', '.join(missing_extensions)}")
        print()
        
        # 3. Migration status
        print("🔄 MIGRATION STATUS:")
        print("-" * 40)
        migration_exists = await conn.fetchval("""
            SELECT EXISTS (SELECT FROM information_schema.tables 
                          WHERE table_schema = 'public' AND table_name = 'schema_migrations')
        """)
        
        if migration_exists:
            migrations = await conn.fetch("SELECT version, executed_at FROM schema_migrations ORDER BY version")
            print(f"   ✅ Migration tracking exists ({len(migrations)} applied)")
            for migration in migrations:
                print(f"      - {migration['version']} (applied: {migration['executed_at']})")
        else:
            print("   ❌ No migration tracking found")
        print()
        
        # 4. Simple table row counts
        print("📋 TABLE ROW COUNTS:")
        print("-" * 40)
        
        # Get all tables
        tables_info = await conn.fetch("""
            SELECT table_name, table_type
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            ORDER BY table_name
        """)
        
        total_tables = len(tables_info)
        total_rows = 0
        empty_tables = []
        
        for table_info in tables_info:
            table_name = table_info['table_name']
            table_type = "VIEW" if table_info['table_type'] == 'VIEW' else "TABLE"
            
            if table_type == "TABLE":
                try:
                    # Get exact count for tables
                    row_count = await conn.fetchval(f"SELECT COUNT(*) FROM {table_name}")
                    total_rows += row_count
                    
                    if row_count == 0:
                        empty_tables.append(table_name)
                        status = "🔴 EMPTY"
                    elif row_count < 10:
                        status = f"🟡 {row_count} rows"
                    elif row_count < 100:
                        status = f"🟢 {row_count} rows"
                    else:
                        status = f"💚 {row_count:,} rows"
                    
                    print(f"   {table_name:<30} {status}")
                    
                except Exception as e:
                    print(f"   {table_name:<30} ❌ Error: {e}")
            else:
                print(f"   {table_name:<30} 📋 VIEW")
        
        print()
        
        # 5. Summary statistics
        print("📊 DATABASE SUMMARY:")
        print("-" * 40)
        print(f"   📋 Total tables: {total_tables}")
        print(f"   📈 Total estimated rows: {total_rows:,}")
        print(f"   🔴 Empty tables: {len(empty_tables)}")
        if empty_tables:
            print(f"      Empty: {', '.join(empty_tables)}")
        print()
        
        # 6. Expected tables check (based on Revisionary schema)
        print("🔍 EXPECTED TABLES VERIFICATION:")
        print("-" * 40)
        expected_core_tables = {
            'users', 'documents', 'blocks', 'versions', 'suggestions', 'summaries',
            'collaborations', 'comments', 'document_scores', 'user_writing_stats',
            'writing_achievements', 'writing_challenges', 'scoring_algorithms',
            'usage_events', 'token_usage_daily', 'personas', 'persona_feedback',
            'audience_analysis', 'custom_agents', 'agent_style_rules',
            'agent_usage_stats', 'health_metrics', 'health_issues', 'entities',
            'entity_relationships', 'consistency_violations', 'citations',
            'reference_library', 'export_jobs', 'templates', 'schema_migrations'
        }
        
        actual_tables = {table['table_name'] for table in tables_info}
        missing_tables = expected_core_tables - actual_tables
        unexpected_tables = actual_tables - expected_core_tables - {'test'}  # Exclude test table
        
        if missing_tables:
            print(f"   ❌ Missing expected tables ({len(missing_tables)}):")
            for table in sorted(missing_tables):
                print(f"      - {table}")
        else:
            print("   ✅ All expected core tables are present")
        
        if unexpected_tables:
            print(f"   ⚠️  Unexpected tables found ({len(unexpected_tables)}):")
            for table in sorted(unexpected_tables):
                print(f"      - {table}")
        
        # 7. Foreign key relationships
        print("\n🔗 FOREIGN KEY RELATIONSHIPS:")
        print("-" * 40)
        fk_relationships = await conn.fetch("""
            SELECT 
                tc.table_name as source_table,
                kcu.column_name as source_column,
                ccu.table_name as target_table,
                ccu.column_name as target_column
            FROM information_schema.table_constraints tc
            JOIN information_schema.key_column_usage kcu 
                ON tc.constraint_name = kcu.constraint_name
            JOIN information_schema.constraint_column_usage ccu 
                ON ccu.constraint_name = tc.constraint_name
            WHERE tc.constraint_type = 'FOREIGN KEY'
            AND tc.table_schema = 'public'
            ORDER BY tc.table_name, kcu.column_name
        """)
        
        fk_count = len(fk_relationships)
        print(f"   📊 Total foreign key constraints: {fk_count}")
        
        # Group by source table
        fk_by_table = {}
        for fk in fk_relationships:
            source = fk['source_table']
            if source not in fk_by_table:
                fk_by_table[source] = []
            fk_by_table[source].append(f"{fk['source_column']} → {fk['target_table']}.{fk['target_column']}")
        
        for table, relationships in sorted(fk_by_table.items()):
            print(f"   📄 {table}:")
            for rel in relationships:
                print(f"      🔗 {rel}")
        
        print()
        print("=" * 80)
        print("✅ Comprehensive schema analysis completed!")
        print("=" * 80)
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Schema analysis failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(check_database_schema())
    if not success:
        exit(1)