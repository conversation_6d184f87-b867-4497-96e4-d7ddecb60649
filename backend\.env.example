# Revisionary Backend Environment Variables

# Development Environment
ENV=development
DEBUG=true
LOG_LEVEL=INFO

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=1

# Database Configuration (Supabase)
DATABASE_URL=postgresql://postgres:password@localhost:5432/revisionary
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your-anon-key
SUPABASE_SERVICE_KEY=your-service-role-key

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_DB=0

# Firebase Authentication
FIREBASE_PROJECT_ID=revisionary-dev
FIREBASE_PRIVATE_KEY_ID=your-private-key-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=your-client-id
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token

# AI/LLM Configuration
OPENAI_API_KEY=sk-your-openai-api-key
GOOGLE_AI_API_KEY=your-google-ai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key

# Model Configuration
DEFAULT_GRAMMAR_MODEL=gpt-4-1106-preview
DEFAULT_STYLE_MODEL=gpt-4-1106-preview
DEFAULT_HEALTH_MODEL=gpt-4-1106-preview
DEFAULT_PERSONA_MODEL=gpt-4-1106-preview

# Mock Data Configuration
USE_MOCK_DATA=true
USE_MOCK_LLM=true
USE_MOCK_AUTH=true
USE_MOCK_REDIS=true

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST=20

# CORS Configuration
CORS_ORIGINS=["http://localhost:3000", "http://localhost:5173"]
CORS_CREDENTIALS=true
CORS_METHODS=["*"]
CORS_HEADERS=["*"]

# WebSocket Configuration
WEBSOCKET_CORS_ORIGINS=["http://localhost:3000", "http://localhost:5173"]

# Security
JWT_SECRET_KEY=your-jwt-secret-key-change-in-production
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# File Upload
MAX_FILE_SIZE_MB=10
ALLOWED_FILE_TYPES=["txt", "md", "docx", "pdf"]

# Monitoring
SENTRY_DSN=
PROMETHEUS_ENABLED=false
PROMETHEUS_PORT=9090