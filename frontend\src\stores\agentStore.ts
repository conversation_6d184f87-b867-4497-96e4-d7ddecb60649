import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { CustomAgent, AgentTemplate, AgentCapability, PredefinedRules } from '../types/agent';

interface AgentStore {
  // State
  agents: CustomAgent[];
  selectedAgents: string[];
  templates: AgentTemplate[];
  
  // Actions
  loadAgents: () => void;
  createAgent: (agent: Omit<CustomAgent, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateAgent: (id: string, updates: Partial<CustomAgent>) => void;
  deleteAgent: (id: string) => void;
  duplicateAgent: (id: string) => void;
  toggleAgentActive: (id: string) => void;
  reorderAgents: (startIndex: number, endIndex: number) => void;
  
  // Selection
  selectAgent: (id: string) => void;
  deselectAgent: (id: string) => void;
  selectAllAgents: () => void;
  deselectAllAgents: () => void;
  
  // Bulk operations
  bulkUpdateAgents: (agentIds: string[], updates: Partial<CustomAgent>) => void;
  bulkDeleteAgents: (agentIds: string[]) => void;
  
  // Import/Export
  exportAgents: (agentIds?: string[]) => string;
  importAgents: (data: string) => void;
  
  // Reset
  resetToDefaults: () => void;
  
  // Getters
  getAgent: (id: string) => CustomAgent | undefined;
  getActiveAgents: () => CustomAgent[];
  getAgentsByCategory: (category: string) => CustomAgent[];
}

// Default capabilities
const defaultCapabilities: AgentCapability[] = [
  {
    id: 'edit',
    action: 'edit',
    name: 'Edit Content',
    description: 'Make corrections and improvements to existing text',
    enabled: true,
  },
  {
    id: 'delete',
    action: 'delete',
    name: 'Delete Content',
    description: 'Remove redundant or unnecessary content',
    enabled: false,
  },
  {
    id: 'add',
    action: 'add',
    name: 'Add Content',
    description: 'Suggest missing information or content',
    enabled: false,
  },
  {
    id: 'restructure',
    action: 'restructure',
    name: 'Restructure',
    description: 'Reorganize content for better flow and organization',
    enabled: false,
  },
  {
    id: 'find-duplicates',
    action: 'find-duplicates',
    name: 'Find Duplicates',
    description: 'Identify repetitive or duplicate content',
    enabled: false,
  },
  {
    id: 'fact-check',
    action: 'fact-check',
    name: 'Fact Check',
    description: 'Verify accuracy of factual claims',
    enabled: false,
  },
  {
    id: 'tone-adjust',
    action: 'tone-adjust',
    name: 'Tone Adjustment',
    description: 'Modify voice and tone to match requirements',
    enabled: false,
  },
];

// Default predefined rules
const defaultPredefinedRules: PredefinedRules = {
  oxfordComma: true,
  sentenceLength: {
    max: 30,
    preferred: 20,
  },
  passiveVoice: {
    maxPercentage: 10,
  },
  paragraphLength: {
    maxSentences: 5,
  },
  vocabulary: {
    preferredTerms: {},
    avoidTerms: [],
  },
};

// Built-in agents (enhanced from our previous implementation)
const builtInAgents: CustomAgent[] = [
  {
    id: 'grammar-agent',
    name: 'Grammar Expert',
    description: 'Focuses on grammar, spelling, and mechanical correctness',
    specialty: 'Grammar & Mechanics',
    color: 'blue',
    systemPrompt: 'You are a grammar expert. Focus on correcting spelling, grammar, punctuation, and mechanical errors. Be precise and explain the rules behind your corrections.',
    capabilities: defaultCapabilities.map(cap => ({
      ...cap,
      enabled: cap.action === 'edit' || cap.action === 'delete',
    })),
    tools: [
      { id: 'grammar-check', name: 'Grammar Checker', description: 'Detect and fix grammatical errors', category: 'grammar', enabled: true },
      { id: 'punctuation-analysis', name: 'Punctuation Analysis', description: 'Check punctuation usage and consistency', category: 'grammar', enabled: true },
      { id: 'spelling-check', name: 'Spelling Checker', description: 'Identify and correct spelling mistakes', category: 'grammar', enabled: true },
    ],
    styleRules: [],
    predefinedRules: {
      ...defaultPredefinedRules,
      oxfordComma: true,
    },
    customInstructions: 'Apply Chicago Manual of Style rules. Prioritize clarity and correctness.',
    isBuiltIn: true,
    isActive: true,
    priority: 1,
    tags: ['grammar', 'spelling', 'punctuation'],
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'style-agent',
    name: 'Style Specialist',
    description: 'Improves writing style, clarity, and readability',
    specialty: 'Style & Clarity',
    color: 'green',
    systemPrompt: 'You are a style expert. Focus on improving sentence structure, word choice, clarity, and readability. Make writing more engaging and professional.',
    capabilities: defaultCapabilities.map(cap => ({
      ...cap,
      enabled: cap.action === 'edit' || cap.action === 'restructure' || cap.action === 'tone-adjust',
    })),
    tools: [
      { id: 'tone-analyzer', name: 'Tone Analyzer', description: 'Analyze and adjust writing tone', category: 'style', enabled: true },
      { id: 'readability-scorer', name: 'Readability Scorer', description: 'Assess text complexity and readability', category: 'style', enabled: true },
      { id: 'voice-consistency', name: 'Voice Consistency', description: 'Ensure consistent voice throughout', category: 'style', enabled: true },
      { id: 'conciseness-optimizer', name: 'Conciseness Optimizer', description: 'Suggest more concise phrasing', category: 'optimization', enabled: true },
    ],
    styleRules: [],
    predefinedRules: {
      ...defaultPredefinedRules,
      sentenceLength: { max: 25, preferred: 18 },
      passiveVoice: { maxPercentage: 15 },
    },
    customInstructions: 'Favor active voice and concise language. Eliminate jargon when possible.',
    isBuiltIn: true,
    isActive: true,
    priority: 2,
    tags: ['style', 'clarity', 'readability'],
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'content-agent',
    name: 'Content Strategist',
    description: 'Ensures logical flow, completeness, and audience alignment',
    specialty: 'Content Strategy',
    color: 'purple',
    systemPrompt: 'You are a content strategist. Focus on logical flow, argument structure, completeness, and audience alignment. Identify missing information and suggest improvements.',
    capabilities: defaultCapabilities.map(cap => ({
      ...cap,
      enabled: cap.action === 'add' || cap.action === 'restructure' || cap.action === 'fact-check',
    })),
    tools: [
      { id: 'structure-analyzer', name: 'Structure Analyzer', description: 'Evaluate document organization', category: 'structure', enabled: true },
      { id: 'flow-checker', name: 'Flow Checker', description: 'Assess logical flow and transitions', category: 'structure', enabled: true },
      { id: 'completeness-checker', name: 'Completeness Checker', description: 'Identify missing content or gaps', category: 'content', enabled: true },
      { id: 'relevance-analyzer', name: 'Relevance Analyzer', description: 'Assess content relevance to topic', category: 'content', enabled: true },
    ],
    styleRules: [],
    predefinedRules: defaultPredefinedRules,
    customInstructions: 'Ensure all arguments are well-supported and the content serves the intended purpose.',
    isBuiltIn: true,
    isActive: true,
    priority: 3,
    tags: ['content', 'strategy', 'structure'],
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'redundancy-agent',
    name: 'Redundancy Hunter',
    description: 'Finds and eliminates repetitive or duplicate content',
    specialty: 'Content Optimization',
    color: 'orange',
    systemPrompt: 'You are a redundancy specialist. Identify repetitive content, duplicate information, and unnecessary repetition. Suggest consolidation and removal.',
    capabilities: defaultCapabilities.map(cap => ({
      ...cap,
      enabled: cap.action === 'delete' || cap.action === 'find-duplicates' || cap.action === 'restructure',
    })),
    tools: [
      { id: 'duplicate-detector', name: 'Duplicate Detector', description: 'Find repetitive or duplicate content', category: 'optimization', enabled: true },
      { id: 'redundancy-eliminator', name: 'Redundancy Eliminator', description: 'Remove unnecessary repetition', category: 'optimization', enabled: true },
      { id: 'conciseness-optimizer', name: 'Conciseness Optimizer', description: 'Suggest more concise phrasing', category: 'optimization', enabled: true },
    ],
    styleRules: [],
    predefinedRules: defaultPredefinedRules,
    customInstructions: 'Be aggressive in identifying redundancy. Preserve meaning while eliminating repetition.',
    isBuiltIn: true,
    isActive: false,
    priority: 4,
    tags: ['efficiency', 'redundancy', 'optimization'],
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'brand-voice-agent',
    name: 'Brand Voice Guardian',
    description: 'Maintains consistent tone and brand voice throughout',
    specialty: 'Brand Consistency',
    color: 'pink',
    systemPrompt: 'You are a brand voice specialist. Ensure consistent tone, voice, and messaging that aligns with brand guidelines and target audience.',
    capabilities: defaultCapabilities.map(cap => ({
      ...cap,
      enabled: cap.action === 'edit' || cap.action === 'tone-adjust',
    })),
    tools: [
      { id: 'tone-analyzer', name: 'Tone Analyzer', description: 'Analyze and adjust writing tone', category: 'style', enabled: true },
      { id: 'voice-consistency', name: 'Voice Consistency', description: 'Ensure consistent voice throughout', category: 'style', enabled: true },
      { id: 'relevance-analyzer', name: 'Relevance Analyzer', description: 'Assess content relevance to topic', category: 'content', enabled: true },
    ],
    styleRules: [],
    predefinedRules: defaultPredefinedRules,
    customInstructions: 'Maintain professional yet approachable tone. Ensure consistency with brand personality.',
    isBuiltIn: true,
    isActive: false,
    priority: 5,
    tags: ['brand', 'voice', 'consistency'],
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

export const useAgentStore = create<AgentStore>()(
  persist(
    (set, get) => ({
      agents: builtInAgents,
      selectedAgents: ['grammar-agent', 'style-agent', 'content-agent'],
      templates: [],

      loadAgents: () => {
        // Load agents from localStorage or API
        const state = get();
        if (state.agents.length === 0) {
          set({ agents: builtInAgents });
        }
      },

      createAgent: (agentData) => {
        const newAgent: CustomAgent = {
          ...agentData,
          id: `agent-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          capabilities: agentData.capabilities.length > 0 ? agentData.capabilities : defaultCapabilities,
          predefinedRules: agentData.predefinedRules || defaultPredefinedRules,
          isBuiltIn: false,
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        set((state) => ({
          agents: [...state.agents, newAgent],
        }));
      },

      updateAgent: (id, updates) => {
        set((state) => ({
          agents: state.agents.map((agent) =>
            agent.id === id
              ? { ...agent, ...updates, updatedAt: new Date() }
              : agent
          ),
        }));
      },

      deleteAgent: (id) => {
        set((state) => ({
          agents: state.agents.filter((agent) => agent.id !== id && !agent.isBuiltIn),
          selectedAgents: state.selectedAgents.filter((agentId) => agentId !== id),
        }));
      },

      duplicateAgent: (id) => {
        const agent = get().agents.find((a) => a.id === id);
        if (agent) {
          const duplicated: CustomAgent = {
            ...agent,
            id: `agent-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            name: `${agent.name} (Copy)`,
            isBuiltIn: false,
            createdAt: new Date(),
            updatedAt: new Date(),
          };

          set((state) => ({
            agents: [...state.agents, duplicated],
          }));
        }
      },

      toggleAgentActive: (id) => {
        set((state) => ({
          agents: state.agents.map((agent) =>
            agent.id === id ? { ...agent, isActive: !agent.isActive } : agent
          ),
        }));
      },

      reorderAgents: (startIndex, endIndex) => {
        set((state) => {
          const result = Array.from(state.agents);
          const [removed] = result.splice(startIndex, 1);
          result.splice(endIndex, 0, removed);

          // Update priorities
          return {
            agents: result.map((agent, index) => ({
              ...agent,
              priority: index + 1,
            })),
          };
        });
      },

      selectAgent: (id) => {
        set((state) => ({
          selectedAgents: state.selectedAgents.includes(id)
            ? state.selectedAgents
            : [...state.selectedAgents, id],
        }));
      },

      deselectAgent: (id) => {
        set((state) => ({
          selectedAgents: state.selectedAgents.filter((agentId) => agentId !== id),
        }));
      },

      selectAllAgents: () => {
        set((state) => ({
          selectedAgents: state.agents.filter(agent => agent.isActive).map(agent => agent.id),
        }));
      },

      deselectAllAgents: () => {
        set({ selectedAgents: [] });
      },

      bulkUpdateAgents: (agentIds, updates) => {
        set((state) => ({
          agents: state.agents.map((agent) =>
            agentIds.includes(agent.id)
              ? { ...agent, ...updates, updatedAt: new Date() }
              : agent
          ),
        }));
      },

      bulkDeleteAgents: (agentIds) => {
        set((state) => ({
          agents: state.agents.filter((agent) => !agentIds.includes(agent.id) || agent.isBuiltIn),
          selectedAgents: state.selectedAgents.filter((id) => !agentIds.includes(id)),
        }));
      },

      exportAgents: (agentIds) => {
        const state = get();
        const agentsToExport = agentIds
          ? state.agents.filter((agent) => agentIds.includes(agent.id))
          : state.agents.filter((agent) => !agent.isBuiltIn);

        return JSON.stringify(agentsToExport, null, 2);
      },

      importAgents: (data) => {
        try {
          const importedAgents: CustomAgent[] = JSON.parse(data);
          
          set((state) => ({
            agents: [
              ...state.agents,
              ...importedAgents.map((agent) => ({
                ...agent,
                id: `agent-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                isBuiltIn: false,
                createdAt: new Date(),
                updatedAt: new Date(),
              })),
            ],
          }));
        } catch (error) {
          console.error('Failed to import agents:', error);
        }
      },

      resetToDefaults: () => {
        set({
          agents: builtInAgents,
          selectedAgents: ['grammar-agent', 'style-agent', 'content-agent'],
        });
      },

      getAgent: (id) => {
        return get().agents.find((agent) => agent.id === id);
      },

      getActiveAgents: () => {
        return get().agents.filter((agent) => agent.isActive);
      },

      getAgentsByCategory: (category) => {
        return get().agents.filter((agent) => agent.tags.includes(category));
      },
    }),
    {
      name: 'agent-store',
      version: 1,
    }
  )
);