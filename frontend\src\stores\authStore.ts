// Zustand store for authentication state with Firebase optimization
import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { onAuthStateChanged, User as FirebaseUser } from 'firebase/auth';
import { auth, firebaseAuth } from '@/services/firebase';
import type { User, AuthState, LoginCredentials, SignupCredentials } from '@/types/auth';

interface AuthStore extends AuthState {
  // Actions
  initialize: () => void;
  signIn: (credentials: LoginCredentials) => Promise<void>;
  signUp: (credentials: SignupCredentials) => Promise<void>;
  signOut: () => Promise<void>;
  refreshUser: () => Promise<void>;
  updateTokenUsage: (used: number, limit: number) => void;
  
  // Internal state
  _setUser: (user: User | null) => void;
  _setLoading: (loading: boolean) => void;
}

const mapFirebaseUserToUser = async (firebaseUser: FirebaseUser | null): Promise<User | null> => {
  if (!firebaseUser) return null;

  try {
    // Get cached user data first (fast)
    let userData = firebaseAuth.getCachedUserData();
    
    // If no cache, fetch from API (slower but necessary)
    if (!userData) {
      const token = await firebaseAuth.getValidToken();
      if (token) {
        const response = await fetch(`${import.meta.env.VITE_API_URL}/api/v1/users/profile`, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });
        
        if (response.ok) {
          userData = await response.json();
        }
      }
    }

    // Fallback to basic Firebase data if API fails
    if (!userData) {
      userData = {
        subscriptionTier: 'free',
        tokenUsage: { used: 0, limit: 10000, resetDate: new Date() },
        preferences: {
          theme: 'system',
          language: 'en',
          defaultDocumentType: 'general',
        },
      };
    }

    return {
      id: firebaseUser.uid,
      email: firebaseUser.email || '',
      displayName: firebaseUser.displayName || undefined,
      photoURL: firebaseUser.photoURL || undefined,
      subscriptionTier: userData.subscriptionTier || 'free',
      tokenUsage: {
        used: userData.tokenUsage?.used || 0,
        limit: userData.tokenUsage?.limit || 10000,
        resetDate: new Date(userData.tokenUsage?.resetDate || Date.now()),
      },
      preferences: {
        theme: userData.preferences?.theme || 'system',
        language: userData.preferences?.language || 'en',
        defaultDocumentType: userData.preferences?.defaultDocumentType || 'general',
      },
    };
  } catch (error) {
    console.error('Error mapping Firebase user:', error);
    return null;
  }
};

export const useAuthStore = create<AuthStore>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    user: null,
    isLoading: true,
    isAuthenticated: false,
    lastTokenCheck: null,

    // Actions
    initialize: () => {
      console.log('Initializing auth store...');
      
      // Check if we're in dev mode
      const isDevMode = import.meta.env.VITE_DEV_MODE === 'true';
      
      if (isDevMode) {
        console.log('Dev mode: Creating mock user');
        // Create a mock user for development
        const mockUser: User = {
          id: 'dev-user-123',
          email: import.meta.env.VITE_DEV_USER_EMAIL || '<EMAIL>',
          displayName: import.meta.env.VITE_DEV_USER_NAME || 'Developer User',
          subscriptionTier: 'professional',
          tokenUsage: {
            used: 5000,
            limit: 225000,
            resetDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
          },
          preferences: {
            theme: 'system',
            language: 'en',
            defaultDocumentType: 'general',
          },
        };
        
        set({
          user: mockUser,
          isAuthenticated: true,
          isLoading: false,
          lastTokenCheck: new Date(),
        });
        
        console.log('Dev mode: Mock user created:', mockUser.email);
        return;
      }
      
      if (!auth) {
        console.error('Firebase auth not initialized');
        set({ isLoading: false });
        return;
      }

      // Single Firebase auth state listener
      const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
        console.log('Auth state changed:', firebaseUser?.uid || 'null');
        
        set({ isLoading: true });
        
        try {
          const user = await mapFirebaseUserToUser(firebaseUser);
          
          set({
            user,
            isAuthenticated: !!user,
            isLoading: false,
            lastTokenCheck: new Date(),
          });
          
          console.log('Auth state updated:', user?.email || 'signed out');
        } catch (error) {
          console.error('Error in auth state change:', error);
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
          });
        }
      });

      // Store unsubscribe function for cleanup
      (get as any)._unsubscribe = unsubscribe;
    },

    signIn: async (credentials: LoginCredentials) => {
      const isDevMode = import.meta.env.VITE_DEV_MODE === 'true';
      
      if (isDevMode) {
        console.log('Dev mode: Simulating sign in');
        // In dev mode, just reload the mock user
        get().initialize();
        return;
      }
      
      try {
        set({ isLoading: true });
        await firebaseAuth.signIn(credentials.email, credentials.password);
        // Auth state change will update the store automatically
      } catch (error) {
        set({ isLoading: false });
        throw error;
      }
    },

    signUp: async (credentials: SignupCredentials) => {
      const isDevMode = import.meta.env.VITE_DEV_MODE === 'true';
      
      if (isDevMode) {
        console.log('Dev mode: Simulating sign up');
        // In dev mode, just reload the mock user
        get().initialize();
        return;
      }
      
      try {
        set({ isLoading: true });
        await firebaseAuth.signUp(credentials.email, credentials.password, credentials.displayName);
        // Auth state change will update the store automatically
      } catch (error) {
        set({ isLoading: false });
        throw error;
      }
    },

    signOut: async () => {
      const isDevMode = import.meta.env.VITE_DEV_MODE === 'true';
      
      if (isDevMode) {
        console.log('Dev mode: Simulating sign out');
        set({
          user: null,
          isAuthenticated: false,
          isLoading: false,
        });
        return;
      }
      
      try {
        await firebaseAuth.signOut();
        // Auth state change will clear the store automatically
      } catch (error) {
        console.error('Sign out error:', error);
        throw error;
      }
    },

    refreshUser: async () => {
      if (!auth) return;
      const currentUser = auth.currentUser;
      if (currentUser) {
        const user = await mapFirebaseUserToUser(currentUser);
        set({ user, lastTokenCheck: new Date() });
      }
    },

    updateTokenUsage: (used: number, limit: number) => {
      const { user } = get();
      if (user) {
        set({
          user: {
            ...user,
            tokenUsage: {
              ...user.tokenUsage,
              used,
              limit,
            },
          },
        });
      }
    },

    // Internal actions
    _setUser: (user: User | null) => {
      set({ user, isAuthenticated: !!user });
    },

    _setLoading: (isLoading: boolean) => {
      set({ isLoading });
    },
  }))
);

// Selector hooks for optimized re-renders
export const useAuth = () => useAuthStore((state) => ({
  user: state.user,
  isLoading: state.isLoading,
  isAuthenticated: state.isAuthenticated,
}));

export const useAuthActions = () => useAuthStore((state) => ({
  signIn: state.signIn,
  signUp: state.signUp,
  signOut: state.signOut,
  refreshUser: state.refreshUser,
}));

export const useUser = () => useAuthStore((state) => state.user);
export const useTokenUsage = () => useAuthStore((state) => state.user?.tokenUsage);