# Revisionary - Frontend Technical Specification

## 1. Technology Stack

### 1.1 Core Technologies
- **Framework**: React 18.2+ with TypeScript 5.0+
- **Build Tool**: Vite 5.0 (for fast HMR and builds)
- **Styling**: Tailwind CSS 3.3 + CSS Modules
- **State Management**: 
  - <PERSON><PERSON><PERSON> for global state
  - React Query (TanStack Query) for server state
  - Jotai for atomic state management
- **Rich Text Editor**: Lexical (by Meta) for extensibility
- **Real-time**: Socket.io client for WebSocket connections

### 1.2 Development Tools
- **Linting**: ESLint with Airbnb config
- **Formatting**: Prettier
- **Testing**: Vitest + React Testing Library
- **E2E Testing**: Playwright
- **Type Checking**: TypeScript strict mode

## 2. Application Architecture

### 2.1 Project Structure
```
src/
├── components/
│   ├── editor/
│   │   ├── Editor.tsx
│   │   ├── MobileEditor.tsx
│   │   ├── EditorToolbar.tsx
│   │   ├── EditorHeader.tsx
│   │   ├── DocumentHeader.tsx
│   │   ├── DocumentOutline.tsx
│   │   ├── AIPanel.tsx
│   │   ├── EnhancedAIPanel.tsx
│   │   ├── WritingHealthPanel.tsx
│   │   ├── WritingHealthBadge.tsx
│   │   ├── DocumentAnalyticsPanel.tsx
│   │   ├── CommentsPanel.tsx
│   │   ├── CitationManager.tsx
│   │   ├── SmartCitationSearch.tsx
│   │   ├── VersionControl.tsx
│   │   ├── LivePresence.tsx
│   │   ├── LeftToolbar.tsx
│   │   ├── StatusBar.tsx
│   │   ├── CommandPalette.tsx
│   │   ├── ImportWizard.tsx
│   │   └── EnterpriseEditor.tsx
│   ├── persona/
│   │   ├── PersonaSelector.tsx
│   │   ├── PersonaCreationWizard.tsx
│   │   └── PersonaFeedbackPanel.tsx
│   ├── settings/
│   │   ├── AgentManager.tsx
│   │   └── AgentEditor.tsx
│   ├── analytics/
│   │   ├── WritingHeatmap.tsx
│   │   ├── ProgressChart.tsx
│   │   ├── AchievementNotification.tsx
│   │   └── WritingGoalsManager.tsx
│   ├── common/
│   │   ├── Button.tsx
│   │   ├── Modal.tsx
│   │   └── LoadingScreen.tsx
│   └── layout/
│       ├── Header.tsx
│       ├── Sidebar.tsx
│       └── Layout.tsx
├── hooks/
│   ├── useDocument.ts
│   ├── useAIGeneration.ts
│   ├── useRealtime.ts
│   ├── useKeyboardShortcuts.ts
│   ├── usePersonas.ts
│   ├── useCustomAgents.ts
│   ├── useWritingHealth.ts
│   ├── useAnalytics.ts
│   └── useCrossReference.ts
├── services/
│   ├── api/
│   │   ├── documents.ts
│   │   ├── ai.ts
│   │   ├── auth.ts
│   │   ├── personas.ts
│   │   ├── agents.ts
│   │   ├── analytics.ts
│   │   └── health.ts
│   ├── websocket/
│   │   └── client.ts
│   └── storage/
│       └── offline.ts
├── stores/
│   ├── documentStore.ts
│   ├── uiStore.ts
│   ├── userStore.ts
│   ├── personaStore.ts
│   ├── agentStore.ts
│   ├── analyticsStore.ts
│   └── healthStore.ts
├── types/
│   ├── document.ts
│   ├── ai.ts
│   ├── user.ts
│   ├── persona.ts
│   ├── agent.ts
│   ├── analytics.ts
│   ├── health.ts
│   └── crossReference.ts
├── workers/
│   └── WritingHealthWorker.ts
└── utils/
    ├── editor/
    ├── formatting/
    ├── performance/
    ├── mobile/
    └── accessibility/
```

### 2.2 Component Architecture

#### Editor Component
```typescript
interface EditorProps {
  documentId: string;
  userId: string;
  onSave: (content: DocumentContent) => void;
  collaborators?: Collaborator[];
}

// Features:
// - Virtual scrolling for large documents
// - Block-based rendering
// - Real-time collaboration cursors
// - Inline AI suggestions
// - Command palette (Cmd+K)
```

#### AI Suggestion System
```typescript
interface AISuggestion {
  id: string;
  agentType: 'grammar' | 'style' | 'structure' | 'content' | 'custom';
  severity: 'low' | 'medium' | 'high' | 'critical';
  originalText: string;
  suggestedText: string;
  explanation: string;
  confidence: number;
  position: TextPosition;
  customAgentId?: string;
}
```

#### Persona System
```typescript
interface ReaderPersona {
  id: string;
  name: string;
  type: PersonaType;
  demographics: Demographics;
  readingPreferences: ReadingPreferences;
  personalityTraits: PersonalityTraits;
  feedbackStyle: FeedbackStyle;
  isActive: boolean;
}

interface PersonaFeedback {
  personaId: string;
  comprehension: ComprehensionAnalysis;
  engagement: EngagementAssessment;
  emotional: EmotionalResponse;
  style: StyleEvaluation;
  suggestions: PersonaSuggestion[];
}
```

#### Custom Agent System
```typescript
interface CustomAgent {
  id: string;
  name: string;
  description: string;
  type: AgentType;
  capabilities: AgentCapability[];
  styleRules: StyleRules;
  priority: number;
  isActive: boolean;
  usage: AgentUsageStats;
}
```

#### Writing Health Analysis
```typescript
interface WritingHealthMetrics {
  readability: ReadabilityScore;
  clarity: ClarityScore;
  voice: VoiceConsistency;
  inclusivity: InclusivityScore;
  brandAlignment: BrandScore;
  overallScore: number;
  issues: HealthIssue[];
}
```

## 3. Editor Implementation

### 3.1 Lexical Editor Configuration
```typescript
const editorConfig = {
  namespace: 'Revisionary',
  theme: editorTheme,
  nodes: [
    HeadingNode,
    QuoteNode,
    CodeNode,
    TableNode,
    ListNode,
    ListItemNode,
    HorizontalRuleNode,
    ImageNode,
    InlineAINode, // Custom node for AI suggestions
    CommentNode,  // Custom node for comments
  ],
  onError: (error: Error) => {
    console.error('Editor error:', error);
  },
};
```

### 3.2 Virtual Scrolling
- **Library**: react-window with custom extensions
- **Block Size**: Dynamic based on content type
- **Buffer**: 5 blocks above/below viewport
- **Performance**: Maintain 60fps with 100k+ word documents

### 3.3 Real-time Collaboration
```typescript
interface CollaborationFeatures {
  cursors: Map<userId, CursorPosition>;
  selections: Map<userId, SelectionRange>;
  presence: UserPresence[];
  awareness: YjsAwareness; // Using Yjs for CRDT
}
```

## 4. State Management

### 4.1 Document Store (Zustand)
```typescript
interface DocumentStore {
  documents: Map<string, Document>;
  activeDocument: string | null;
  blocks: Map<string, Block>;
  entities: Map<string, Entity>;
  crossReferences: CrossReference[];
  
  // Actions
  loadDocument: (id: string) => Promise<void>;
  updateBlock: (blockId: string, content: string) => void;
  saveDocument: () => Promise<void>;
  
  // Entity and consistency management
  addEntity: (entity: Entity) => void;
  updateEntity: (entityId: string, updates: Partial<Entity>) => void;
  checkConsistency: () => ConsistencyReport;
  
  // Optimistic updates
  optimisticUpdate: (update: DocumentUpdate) => void;
  revertOptimisticUpdate: (updateId: string) => void;
}
```

### 4.2 AI State Management
```typescript
interface AIStore {
  suggestions: Map<blockId, AISuggestion[]>;
  activeGeneration: GenerationTask | null;
  generationHistory: GenerationHistoryItem[];
  customAgents: Map<string, CustomAgent>;
  activePersonas: ReaderPersona[];
  personaFeedback: Map<string, PersonaFeedback[]>;
  
  // Actions
  requestSuggestions: (blockId: string, agentMask: AgentMask) => void;
  acceptSuggestion: (suggestionId: string) => void;
  dismissSuggestion: (suggestionId: string) => void;
  
  // Custom agent management
  createCustomAgent: (agent: CustomAgentConfig) => Promise<CustomAgent>;
  updateCustomAgent: (agentId: string, updates: Partial<CustomAgent>) => void;
  deleteCustomAgent: (agentId: string) => void;
  
  // Persona management
  addPersona: (persona: ReaderPersona) => void;
  removePersona: (personaId: string) => void;
  requestPersonaFeedback: (blockId: string, personas: string[]) => void;
  
  // Caching
  cachedResponses: LRUCache<string, AIResponse>;
}
```

### 4.3 Writing Health State Management
```typescript
interface HealthStore {
  currentMetrics: WritingHealthMetrics | null;
  historicalData: HealthHistoryPoint[];
  activeIssues: HealthIssue[];
  
  // Actions
  analyzeText: (text: string) => Promise<WritingHealthMetrics>;
  resolveIssue: (issueId: string) => void;
  updatePreferences: (preferences: HealthPreferences) => void;
}
```

### 4.4 Analytics Store
```typescript
interface AnalyticsStore {
  currentSession: WritingSession | null;
  goals: WritingGoal[];
  achievements: Achievement[];
  progressData: ProgressDataPoint[];
  
  // Actions
  startSession: () => void;
  endSession: () => void;
  updateGoal: (goalId: string, progress: number) => void;
  unlockAchievement: (achievementId: string) => void;
  trackEvent: (event: AnalyticsEvent) => void;
}
```

## 5. UI/UX Implementation

### 5.1 Design System
```typescript
// Theme configuration
const theme = {
  colors: {
    primary: { /* Shades 50-900 */ },
    neutral: { /* Shades 50-950 */ },
    success: { /* Green shades */ },
    warning: { /* Yellow shades */ },
    error: { /* Red shades */ },
  },
  typography: {
    fontFamily: {
      sans: ['Inter', 'system-ui'],
      serif: ['Merriweather', 'Georgia'],
      mono: ['JetBrains Mono', 'monospace'],
    },
  },
  spacing: { /* 4px base unit */ },
  animation: {
    timing: {
      fast: '150ms',
      normal: '300ms',
      slow: '500ms',
    },
  },
};
```

### 5.2 Responsive Design
- **Breakpoints**: 
  - Mobile: 0-640px
  - Tablet: 641-1024px
  - Desktop: 1025px+
- **Layout**: Collapsible sidebar on mobile
- **Touch**: Gesture support for mobile editing

### 5.3 Accessibility
```typescript
// Accessibility utilities
const a11y = {
  announcements: new AriaLiveRegion('polite'),
  focusTrap: new FocusTrap(),
  keyboardNav: new KeyboardNavigator(),
};

// Screen reader announcements
const announceAISuggestion = (suggestion: AISuggestion) => {
  a11y.announcements.announce(
    `${suggestion.agentType} suggestion: ${suggestion.explanation}`
  );
};
```

## 6. Performance Optimizations

### 6.1 Code Splitting
```typescript
// Lazy load heavy components
const AIPanel = lazy(() => import('./components/ai/AIPanel'));
const ExportModal = lazy(() => import('./components/export/ExportModal'));
const SettingsPanel = lazy(() => import('./components/settings/SettingsPanel'));
```

### 6.2 Memoization Strategy
```typescript
// Expensive computations
const useDocumentStats = (documentId: string) => {
  return useMemo(() => {
    const document = getDocument(documentId);
    return calculateStats(document);
  }, [documentId]);
};

// Component memoization
const SuggestionCard = memo(({ suggestion }: Props) => {
  // Component implementation
}, (prevProps, nextProps) => {
  return prevProps.suggestion.id === nextProps.suggestion.id;
});
```

### 6.3 Virtualization
```typescript
// Document renderer with virtualization
const DocumentRenderer = () => {
  const rowRenderer = useCallback(({ index, style }) => {
    const block = blocks[index];
    return (
      <div style={style}>
        <BlockRenderer block={block} />
      </div>
    );
  }, [blocks]);

  return (
    <VariableSizeList
      height={viewportHeight}
      itemCount={blocks.length}
      itemSize={getBlockHeight}
      overscanCount={5}
    >
      {rowRenderer}
    </VariableSizeList>
  );
};
```

## 7. API Integration

### 7.1 API Client
```typescript
class APIClient {
  private baseURL: string;
  private auth: FirebaseAuth;
  
  constructor() {
    this.baseURL = import.meta.env.VITE_API_URL;
    this.auth = getAuth();
  }
  
  private async getHeaders(): Promise<Headers> {
    const token = await this.auth.currentUser?.getIdToken();
    return new Headers({
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    });
  }
  
  async request<T>(endpoint: string, options?: RequestInit): Promise<T> {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      ...options,
      headers: await this.getHeaders(),
    });
    
    if (!response.ok) {
      throw new APIError(response.status, await response.text());
    }
    
    return response.json();
  }
}
```

### 7.2 React Query Configuration
```typescript
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      retry: 3,
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
    mutations: {
      retry: 1,
    },
  },
});
```

## 8. Mobile Editor Implementation

### 8.1 Mobile-First Design
```typescript
interface MobileEditorProps {
  documentId: string;
  isTablet: boolean;
  orientation: 'portrait' | 'landscape';
  keyboardHeight: number;
}

// Touch-optimized interactions
interface TouchGesture {
  type: GestureType;
  action: EditorAction;
  sensitivity: number;
  hapticFeedback: boolean;
}
```

### 8.2 Responsive Layout System
- **Adaptive UI**: Dynamic layout based on screen size and orientation
- **Touch Targets**: Minimum 44px touch areas for accessibility
- **Gesture Support**: Swipe navigation, pinch-to-zoom, long-press menus
- **Keyboard Integration**: Virtual keyboard optimization and external keyboard support
- **Performance Optimization**: 60fps scrolling and smooth animations

### 8.3 Mobile-Specific Features
```typescript
interface MobileFeatures {
  voiceInput: boolean;
  handwritingRecognition: boolean;
  offlineSync: boolean;
  backgroundSync: boolean;
  notificationSupport: boolean;
}
```

## 9. Analytics and Achievement System

### 9.1 Writing Analytics
```typescript
interface WritingSession {
  id: string;
  startTime: Date;
  endTime: Date;
  wordsWritten: number;
  timeWriting: number;
  aiInteractions: number;
  productivityScore: number;
}

interface WritingGoal {
  id: string;
  type: GoalType;
  target: number;
  current: number;
  timeframe: TimeFrame;
  status: GoalStatus;
}
```

### 9.2 Achievement System
```typescript
interface Achievement {
  id: string;
  name: string;
  description: string;
  category: AchievementCategory;
  rarity: AchievementRarity;
  progress: number;
  unlockedAt?: Date;
}

type AchievementRarity = 'common' | 'rare' | 'epic' | 'legendary';
```

### 9.3 Progress Visualization
- **Writing Heatmap**: Calendar view of writing activity
- **Progress Charts**: Goal achievement and trend analysis
- **Achievement Notifications**: Animated achievement unlocks
- **Streak Tracking**: Consistency monitoring and motivation

## 10. Writing Health System

### 10.1 Real-Time Health Analysis
```typescript
interface WritingHealthWorker {
  analyzeText: (text: string) => Promise<HealthAnalysis>;
  generateSuggestions: (issues: HealthIssue[]) => HealthSuggestion[];
  trackImprovement: (before: HealthMetrics, after: HealthMetrics) => ImprovementReport;
}

interface HealthMetrics {
  readability: ReadabilityScore;
  clarity: ClarityScore;
  voice: VoiceConsistency;
  inclusivity: InclusivityScore;
  overallScore: number;
}
```

### 10.2 Health Dashboard
- **Live Scoring**: Real-time health metrics as user types
- **Issue Highlighting**: Visual indicators for problem areas
- **Improvement Suggestions**: Actionable recommendations
- **Progress Tracking**: Historical health improvement data

## 11. Cross-Reference Intelligence

### 11.1 Entity Management
```typescript
interface Entity {
  id: string;
  name: string;
  type: EntityType;
  attributes: EntityAttribute[];
  references: DocumentReference[];
  relationships: EntityRelationship[];
}

type EntityType = 'character' | 'location' | 'organization' | 'concept';
```

### 11.2 Consistency Checking
```typescript
interface ConsistencyReport {
  overallScore: number;
  violations: ConsistencyViolation[];
  suggestions: ConsistencySuggestion[];
  entities: EntityConsistency[];
}

interface ConsistencyViolation {
  type: ViolationType;
  severity: SeverityLevel;
  description: string;
  locations: DocumentLocation[];
  suggestions: string[];
}
```

## 12. Real-time Features

### 12.1 WebSocket Connection (Unified /events namespace)
```typescript
class RealtimeConnection {
  private socket: Socket;
  private documentId: string;
  
  connect(documentId: string, userId: string) {
    this.socket = io(WS_URL + '/events', {
      auth: { token: getAuthToken() },
      transports: ['websocket'],
    });
    
    // Subscribe to unified Redis channels
    this.socket.on('ai:suggestions:complete', this.handleNewSuggestion);
    this.socket.on('document:updates', this.handleDocumentUpdate);
    this.socket.on('document:presence', this.handleCursorUpdate);
    this.socket.on('stale:suggestions', this.handleStaleSuggestion);
    this.socket.on('user:limits', this.handleTokenLimitWarning);
  }
  
  private handleStaleSuggestion = (data: { block_id: string, suggestion_ids: string[] }) => {
    // Grey out stale suggestions
    data.suggestion_ids.forEach(id => {
      const suggestionElement = document.querySelector(`[data-suggestion-id="${id}"]`);
      if (suggestionElement) {
        suggestionElement.classList.add('suggestion-stale');
      }
    });
  };
  
  private handleTokenLimitWarning = (data: { tokens_remaining: number, limit: number }) => {
    // Update token budget display
    this.updateTokenBudget(data.tokens_remaining, data.limit);
  };
  
  sendUpdate(update: DocumentUpdate) {
    this.socket.emit('document:update', update);
  }
}
```

### 8.2 Stale Suggestion Badge Implementation
```typescript
interface SuggestionCardProps {
  suggestion: AISuggestion;
  isStale?: boolean;
}

const SuggestionCard: React.FC<SuggestionCardProps> = ({ suggestion, isStale }) => {
  return (
    <div 
      className={`suggestion-card ${isStale ? 'suggestion-stale' : ''}`}
      data-suggestion-id={suggestion.id}
    >
      {isStale && (
        <div className="stale-badge">
          <AlertTriangleIcon className="w-4 h-4" />
          <span>Stale - Text changed</span>
          <button onClick={() => rerunSuggestion(suggestion.blockId)}>
            Re-run
          </button>
        </div>
      )}
      
      <div className={`suggestion-content ${isStale ? 'opacity-50' : ''}`}>
        <h4>{suggestion.agentType} suggestion</h4>
        <p>{suggestion.explanation}</p>
        <div className="suggestion-actions">
          <button disabled={isStale} onClick={() => acceptSuggestion(suggestion)}>
            Accept
          </button>
          <button onClick={() => dismissSuggestion(suggestion)}>
            Dismiss
          </button>
        </div>
      </div>
    </div>
  );
};

// CSS for stale suggestions
const staleStyles = `
.suggestion-stale {
  border-left: 3px solid #f59e0b;
  background-color: #fef3c7;
}

.stale-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background-color: #f59e0b;
  color: white;
  font-size: 0.875rem;
}

.suggestion-stale .suggestion-content {
  opacity: 0.6;
}
`;
```

### 8.3 Token Budget Widget
```typescript
interface TokenBudgetWidgetProps {
  userId: string;
}

const TokenBudgetWidget: React.FC<TokenBudgetWidgetProps> = ({ userId }) => {
  const [budget, setBudget] = useState<TokenBudget | null>(null);
  
  useEffect(() => {
    // Subscribe to token limit updates
    const unsubscribe = subscribeToChannel(`user:${userId}:limits`, (data) => {
      setBudget({
        used: data.tokens_used,
        limit: data.tokens_limit,
        percentage: (data.tokens_used / data.tokens_limit) * 100
      });
    });
    
    // Fetch initial budget
    fetchTokenBudget(userId).then(setBudget);
    
    return unsubscribe;
  }, [userId]);
  
  if (!budget) return null;
  
  const getStatusColor = (percentage: number) => {
    if (percentage >= 90) return 'text-red-500';
    if (percentage >= 75) return 'text-yellow-500';
    return 'text-green-500';
  };
  
  const getStatusMessage = (percentage: number) => {
    if (percentage >= 95) return 'Limit reached';
    if (percentage >= 90) return 'Almost at limit';
    if (percentage >= 75) return 'High usage';
    return 'Good';
  };
  
  return (
    <div className="token-budget-widget">
      <div className="flex items-center gap-2">
        <CreditCardIcon className="w-4 h-4" />
        <span className="text-sm font-medium">Tokens</span>
        <span className={`text-sm ${getStatusColor(budget.percentage)}`}>
          {budget.used.toLocaleString()} / {budget.limit.toLocaleString()}
        </span>
      </div>
      
      <div className="mt-1">
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className={`h-2 rounded-full transition-all duration-300 ${
              budget.percentage >= 90 ? 'bg-red-500' :
              budget.percentage >= 75 ? 'bg-yellow-500' : 'bg-green-500'
            }`}
            style={{ width: `${Math.min(budget.percentage, 100)}%` }}
          />
        </div>
        <div className="text-xs text-gray-500 mt-1">
          {getStatusMessage(budget.percentage)}
        </div>
      </div>
    </div>
  );
};
```

### 8.2 Optimistic Updates
```typescript
const useOptimisticUpdate = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: updateDocument,
    onMutate: async (newData) => {
      await queryClient.cancelQueries(['document', newData.id]);
      const previousData = queryClient.getQueryData(['document', newData.id]);
      
      // Optimistically update
      queryClient.setQueryData(['document', newData.id], newData);
      
      return { previousData };
    },
    onError: (err, newData, context) => {
      // Rollback
      queryClient.setQueryData(
        ['document', newData.id],
        context.previousData
      );
    },
  });
};
```

## 9. Offline Support

### 9.1 Service Worker
```typescript
// sw.ts
self.addEventListener('fetch', (event) => {
  if (event.request.url.includes('/api/documents')) {
    event.respondWith(
      caches.match(event.request).then((response) => {
        return response || fetch(event.request).then((response) => {
          return caches.open('v1').then((cache) => {
            cache.put(event.request, response.clone());
            return response;
          });
        });
      })
    );
  }
});
```

### 9.2 IndexedDB Storage
```typescript
class OfflineStorage {
  private db: IDBDatabase;
  
  async saveDocument(document: Document) {
    const tx = this.db.transaction(['documents'], 'readwrite');
    await tx.objectStore('documents').put(document);
    await tx.complete;
  }
  
  async getDocument(id: string): Promise<Document | null> {
    const tx = this.db.transaction(['documents'], 'readonly');
    return tx.objectStore('documents').get(id);
  }
}
```

## 10. Testing Strategy

### 10.1 Unit Testing (Legacy - See Section 17 for Updated Testing)
```typescript
describe('Editor Component', () => {
  it('should render document content', async () => {
    render(<Editor documentId="123" />);
    await waitFor(() => {
      expect(screen.getByText('Document content')).toBeInTheDocument();
    });
  });
  
  it('should handle AI suggestions', async () => {
    const { getByTestId } = render(<Editor documentId="123" />);
    fireEvent.click(getByTestId('ai-suggest-button'));
    
    await waitFor(() => {
      expect(screen.getByText('Grammar suggestion')).toBeInTheDocument();
    });
  });
});
```

### 10.2 E2E Testing (Legacy - See Section 17 for Updated Testing)
```typescript
test('complete writing workflow', async ({ page }) => {
  await page.goto('/editor/new');
  await page.type('[data-testid="editor"]', 'Once upon a time');
  await page.click('[data-testid="ai-write"]');
  
  await expect(page.locator('.ai-suggestion')).toBeVisible();
  await page.click('[data-testid="accept-suggestion"]');
  
  await expect(page.locator('[data-testid="word-count"]'))
    .toContainText('350 words');
});
```

## 13. Advanced Features Integration

### 13.1 Command Palette
```typescript
interface CommandPaletteAction {
  id: string;
  label: string;
  description: string;
  category: CommandCategory;
  shortcut?: KeyboardShortcut;
  execute: () => Promise<void>;
}

type CommandCategory = 'document' | 'ai' | 'formatting' | 'navigation' | 'persona' | 'agent';
```

### 13.2 Citation Management
```typescript
interface Citation {
  id: string;
  type: CitationType;
  source: CitationSource;
  content: string;
  pageNumber?: number;
  location: DocumentLocation;
}

interface SmartCitationSearch {
  query: string;
  filters: SearchFilter[];
  results: CitationResult[];
  autoComplete: boolean;
}
```

### 13.3 Enterprise Features
```typescript
interface EnterpriseEditor {
  collaborationMode: CollaborationMode;
  workflowIntegration: WorkflowConfig;
  complianceChecking: ComplianceRule[];
  customBranding: BrandingConfig;
  ssoIntegration: SSOConfig;
}
```

## 14. Performance Metrics

### 14.1 Core Web Vitals
- **LCP**: < 2.5s
- **FID**: < 100ms
- **CLS**: < 0.1
- **FCP**: < 1.8s

### 14.2 Custom Metrics
```typescript
// Track editor performance
performance.mark('editor-load-start');
// ... editor initialization
performance.mark('editor-load-end');
performance.measure('editor-load', 'editor-load-start', 'editor-load-end');

// Send to analytics
analytics.track('performance', {
  metric: 'editor-load',
  duration: performance.getEntriesByName('editor-load')[0].duration,
});
```

### 14.3 Mobile Performance
```typescript
// Mobile-specific performance tracking
interface MobilePerformanceMetrics {
  touchResponseTime: number;
  scrollFrameRate: number;
  keyboardShowTime: number;
  batteryUsage: number;
  memoryUsage: number;
}
```

## 15. Accessibility Implementation

### 15.1 Screen Reader Support
```typescript
interface AccessibilityFeatures {
  screenReaderSupport: boolean;
  keyboardNavigation: boolean;
  highContrast: boolean;
  focusManagement: boolean;
  ariaLabels: AriaLabelConfig;
}

// Live announcements for dynamic content
const announceToScreenReader = (message: string, priority: 'polite' | 'assertive') => {
  const announcement = document.createElement('div');
  announcement.setAttribute('aria-live', priority);
  announcement.setAttribute('aria-atomic', 'true');
  announcement.textContent = message;
  document.body.appendChild(announcement);
  setTimeout(() => document.body.removeChild(announcement), 1000);
};
```

### 15.2 Keyboard Navigation
- **Tab Order**: Logical navigation flow through interface
- **Keyboard Shortcuts**: Customizable shortcuts for all actions
- **Focus Indicators**: Clear visual focus states
- **Skip Links**: Quick navigation to main content areas

### 15.3 Visual Accessibility
- **High Contrast Mode**: Enhanced visibility options
- **Font Size Scaling**: User-controlled text size
- **Color Blind Support**: Color-independent information display
- **Motion Sensitivity**: Reduced animation options

## 16. Deployment

### 16.1 Build Configuration
```typescript
// vite.config.ts
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          'react-vendor': ['react', 'react-dom'],
          'editor': ['lexical', '@lexical/react'],
          'ai': ['./src/components/ai'],
        },
      },
    },
    target: 'es2020',
    minify: 'terser',
  },
});
```

### 16.2 Environment Variables
```env
# Core API Configuration
VITE_API_URL=https://api.revisionary.app
VITE_WS_URL=wss://ws.revisionary.app
VITE_FIREBASE_CONFIG={"apiKey":"...","authDomain":"..."}

# Feature Flags
VITE_ENABLE_PERSONAS=true
VITE_ENABLE_CUSTOM_AGENTS=true
VITE_ENABLE_WRITING_HEALTH=true
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_CROSS_REFERENCE=true
VITE_ENABLE_MOBILE_EDITOR=true
VITE_ENABLE_VOICE_INPUT=true

# Analytics and Monitoring
VITE_SENTRY_DSN=https://...@sentry.io/...
VITE_ANALYTICS_TRACKING_ID=GA_TRACKING_ID
VITE_PERFORMANCE_MONITORING=true

# AI and Processing
VITE_HEALTH_WORKER_URL=/workers/WritingHealthWorker.js
VITE_MAX_CONCURRENT_HEALTH_CHECKS=3
VITE_PERSONA_FEEDBACK_DELAY=500

# Mobile Configuration
VITE_MOBILE_BREAKPOINT=768
VITE_TOUCH_THRESHOLD=44
VITE_HAPTIC_FEEDBACK=true

# Enterprise Features
VITE_ENTERPRISE_MODE=false
VITE_SSO_ENABLED=false
VITE_COMPLIANCE_CHECKING=false
```

## 17. Testing Strategy

### 17.1 Component Testing
```typescript
// Persona system testing
describe('PersonaCreationWizard', () => {
  it('should create academic persona with correct configuration', () => {
    // Test persona creation flow
  });
  
  it('should provide multi-persona feedback analysis', () => {
    // Test feedback generation
  });
});

// Custom agent testing
describe('AgentManager', () => {
  it('should create custom agent with style rules', () => {
    // Test agent creation
  });
  
  it('should handle agent execution priorities', () => {
    // Test agent orchestration
  });
});
```

### 17.2 Integration Testing
```typescript
// Health analysis integration
describe('WritingHealthIntegration', () => {
  it('should provide real-time health analysis', async () => {
    const { getByTestId } = render(<WritingHealthPanel />);
    const textInput = getByTestId('editor-input');
    
    fireEvent.change(textInput, { target: { value: 'Test content' } });
    
    await waitFor(() => {
      expect(getByTestId('health-score')).toBeInTheDocument();
    });
  });
});
```

### 17.3 Mobile Testing
```typescript
// Mobile editor testing
describe('MobileEditor', () => {
  it('should handle touch gestures correctly', () => {
    // Test touch interactions
  });
  
  it('should adapt to keyboard visibility', () => {
    // Test keyboard adaptation
  });
});
```

## 18. Feature Implementation Status

### 18.1 Completed Features ✅
- **Persona System**: Complete multi-reader feedback analysis
- **Custom AI Agents**: Advanced agent creation and management
- **Writing Health Analysis**: Real-time quality assessment
- **Analytics & Achievements**: Gamified writing experience
- **Cross-Reference Intelligence**: Consistency management
- **Mobile Editor**: Touch-optimized responsive interface
- **Advanced UI Components**: Command palette, citation management
- **Accessibility Features**: Screen reader and keyboard support

### 18.2 Backend Integration Required 🔄
- **API Endpoints**: All new features need backend implementation
- **Real-time Processing**: Health analysis and persona feedback workers
- **Data Persistence**: Database schema for new entities
- **Authentication**: Feature access control and permissions
- **Performance Optimization**: Caching and scaling for new features

### 18.3 Future Enhancements 🚀
- **Voice Input Integration**: Speech-to-text capabilities
- **Advanced Collaboration**: Multi-user persona sessions
- **AI Model Fine-tuning**: Custom agent training
- **Plugin System**: Third-party integrations
- **Offline Sync**: Full offline capability for mobile

This updated specification reflects the complete current state of the Revisionary frontend implementation, including all advanced features and capabilities.