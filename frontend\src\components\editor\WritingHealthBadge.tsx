import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  HeartIcon,
  ChevronUpIcon,
  SparklesIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';

interface WritingHealthScore {
  overall: number;
  readingLevel: number;
  wordiness: number;
  passiveVoice: number;
  inclusiveLanguage: number;
  brandVoice: number;
}

interface WritingHealthBadgeProps {
  score: WritingHealthScore;
  onOpenPanel: () => void;
  isVisible?: boolean;
}

const WritingHealthBadge: React.FC<WritingHealthBadgeProps> = ({
  score,
  onOpenPanel,
  isVisible = true,
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const getScoreColor = (score: number) => {
    if (score >= 85) return { bg: 'from-emerald-500 to-green-500', text: 'text-green-700', ring: 'ring-green-500/20' };
    if (score >= 70) return { bg: 'from-amber-500 to-yellow-500', text: 'text-yellow-700', ring: 'ring-yellow-500/20' };
    return { bg: 'from-red-500 to-rose-500', text: 'text-red-700', ring: 'ring-red-500/20' };
  };

  const getScoreIcon = (score: number) => {
    if (score >= 85) return CheckCircleIcon;
    if (score >= 70) return ExclamationTriangleIcon;
    return ExclamationTriangleIcon;
  };

  const colors = getScoreColor(score.overall);
  const IconComponent = getScoreIcon(score.overall);

  const radius = 20;
  const circumference = 2 * Math.PI * radius;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (score.overall / 100) * circumference;

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.8, y: 20 }}
          transition={{ type: "spring", duration: 0.6 }}
          className="fixed bottom-6 right-6 z-50"
        >
          <motion.button
            onClick={onOpenPanel}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
            className={`relative group bg-white/90 backdrop-blur-xl rounded-2xl p-4 shadow-xl border border-white/20 ${colors.ring} ring-4 hover:shadow-2xl transition-all duration-300`}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {/* Background gradient glow */}
            <div className={`absolute inset-0 bg-gradient-to-r ${colors.bg} opacity-10 rounded-2xl`} />
            
            {/* Progress Ring */}
            <div className="relative w-12 h-12 mx-auto">
              <svg
                className="w-12 h-12 transform -rotate-90"
                viewBox="0 0 50 50"
              >
                {/* Background circle */}
                <circle
                  cx="25"
                  cy="25"
                  r={radius}
                  stroke="currentColor"
                  strokeWidth="3"
                  fill="none"
                  className="text-slate-200"
                />
                {/* Progress circle */}
                <motion.circle
                  cx="25"
                  cy="25"
                  r={radius}
                  stroke="url(#healthGradient)"
                  strokeWidth="3"
                  fill="none"
                  strokeLinecap="round"
                  style={{
                    strokeDasharray,
                    strokeDashoffset,
                  }}
                  initial={{ strokeDashoffset: circumference }}
                  animate={{ strokeDashoffset }}
                  transition={{ duration: 1, ease: "easeOut" }}
                />
                <defs>
                  <linearGradient id="healthGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor={score.overall >= 85 ? '#10b981' : score.overall >= 70 ? '#f59e0b' : '#ef4444'} />
                    <stop offset="100%" stopColor={score.overall >= 85 ? '#059669' : score.overall >= 70 ? '#d97706' : '#dc2626'} />
                  </linearGradient>
                </defs>
              </svg>
              
              {/* Center content */}
              <div className="absolute inset-0 flex items-center justify-center">
                <motion.div
                  animate={{ rotate: isHovered ? 360 : 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <HeartIcon className={`w-5 h-5 ${colors.text}`} />
                </motion.div>
              </div>
            </div>

            {/* Score text */}
            <div className="mt-2 text-center">
              <motion.div
                className={`text-lg font-bold ${colors.text}`}
                animate={{ scale: isHovered ? 1.1 : 1 }}
                transition={{ duration: 0.2 }}
              >
                {Math.round(score.overall)}
              </motion.div>
              <div className="text-xs text-slate-500 font-medium">Health</div>
            </div>

            {/* Expand indicator */}
            <motion.div
              className="absolute -top-1 -right-1 w-6 h-6 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg"
              animate={{ 
                scale: isHovered ? [1, 1.2, 1] : 1,
                rotate: isHovered ? 180 : 0 
              }}
              transition={{ 
                scale: { duration: 0.6, repeat: isHovered ? Infinity : 0 },
                rotate: { duration: 0.3 }
              }}
            >
              <ChevronUpIcon className="w-3 h-3 text-white" />
            </motion.div>

            {/* Tooltip on hover */}
            <AnimatePresence>
              {isHovered && (
                <motion.div
                  initial={{ opacity: 0, y: 10, scale: 0.8 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: 10, scale: 0.8 }}
                  className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 bg-slate-900 text-white px-3 py-2 rounded-lg text-sm font-medium whitespace-nowrap shadow-xl"
                >
                  <div className="flex items-center space-x-2">
                    <SparklesIcon className="w-4 h-4" />
                    <span>Click for detailed analysis</span>
                  </div>
                  <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-slate-900" />
                </motion.div>
              )}
            </AnimatePresence>
          </motion.button>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default WritingHealthBadge;