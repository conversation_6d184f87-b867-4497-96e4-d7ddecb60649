# This script defines the FastAPI application factory.
# It creates and configures the FastAPI app, including routers, middleware, and event handlers.
# The `run.py` script in the parent directory is the recommended way to run this application for development.

"""
FastAPI Application Entry Point

Purpose: Main FastAPI application configuration and setup including:
- Application initialization with middleware
- Router registration for all API endpoints
- CORS configuration for frontend integration
- Global exception handlers and logging setup

Responsibilities:
- FastAPI app creation and configuration
- Middleware setup (CORS, authentication, rate limiting)
- API router registration and URL routing
- Global error handling and response formatting
- Application startup and shutdown event handlers

Used by: uvicorn server, deployment infrastructure
Dependencies: All API routers, core services, configuration
"""

from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import structlog
import os
from typing import List

# Import API routers
from api.auth import router as auth_router
from api.documents import router as documents_router
from api.blocks import router as blocks_router
from api.agent_management import router as agent_management_router
from api.health import router as health_router

# Import core services
from core.auth import AuthService
from core.settings import settings
from core.database import get_database
from core.redis_service import get_redis

# Configure structured logging
logger = structlog.get_logger(__name__)

def create_app() -> FastAPI:
    """
    Create and configure the FastAPI application.
    
    Returns:
        FastAPI: Configured FastAPI application instance
    """
    
    # Create FastAPI app
    app = FastAPI(
        title="Revisionary API",
        description="AI-powered writing assistant backend API",
        version="1.0.0",
        debug=settings.debug,
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
    )
    
    # Add middleware
    setup_middleware(app)
    
    # Register routers
    register_routers(app)
    
    # Add event handlers
    setup_event_handlers(app)
    
    # Add exception handlers
    setup_exception_handlers(app)
    
    return app

def setup_middleware(app: FastAPI) -> None:
    """Setup application middleware."""
    
    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.api.cors_origins,
        allow_credentials=settings.api.cors_credentials,
        allow_methods=settings.api.cors_methods,
        allow_headers=settings.api.cors_headers,
    )
    
    # Trusted host middleware for production
    if settings.env == "production":
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=["*.revisionary.app", "revisionary.app"]
        )

def register_routers(app: FastAPI) -> None:
    """Register all API routers with their prefixes."""
    
    app.include_router(
        auth_router,
        prefix="/api/v1/auth",
        tags=["authentication"]
    )
    
    app.include_router(
        documents_router,
        prefix="/api/v1/documents",
        tags=["documents"]
    )
    
    app.include_router(
        blocks_router,
        prefix="/api/v1/blocks",
        tags=["blocks"]
    )
    
    app.include_router(
        agent_management_router,
        prefix="/api/v1/agents",
        tags=["agents"]
    )
    
    app.include_router(
        health_router,
        prefix="/api/v1/health",
        tags=["health"]
    )

def setup_event_handlers(app: FastAPI) -> None:
    """Setup application startup and shutdown event handlers."""
    
    @app.on_event("startup")
    async def startup_event():
        """Application startup tasks."""
        logger.info(
            "Starting Revisionary API",
            env=settings.env,
            debug=settings.debug,
            mock_mode=settings.mock.use_mock_data
        )
        
        # Initialize services based on mock settings
        if not settings.mock.use_mock_data:
            # Initialize database service
            db = await get_database()
            logger.info("Database service initialized")
            
            # Initialize Redis service
            if not settings.mock.use_mock_redis:
                redis = await get_redis()
                logger.info("Redis service initialized")
        
        # Initialize authentication service
        if not settings.mock.use_mock_auth:
            await AuthService.initialize()
            logger.info("Authentication service initialized")
        
        logger.info("Revisionary API started successfully")
    
    @app.on_event("shutdown")
    async def shutdown_event():
        """Application shutdown tasks."""
        logger.info("Shutting down Revisionary API")
        
        # Close services if they were initialized
        if not settings.mock.use_mock_data:
            from core.database import db_service
            await db_service.close()
            logger.info("Database service closed")
            
            if not settings.mock.use_mock_redis:
                from core.redis_service import redis_service
                await redis_service.close()
                logger.info("Redis service closed")
        
        logger.info("Revisionary API shutdown complete")

def setup_exception_handlers(app: FastAPI) -> None:
    """Setup global exception handlers."""
    
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        """Handle HTTP exceptions with structured responses."""
        logger.warning(
            "HTTP exception occurred",
            status_code=exc.status_code,
            detail=exc.detail,
            path=request.url.path
        )
        
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "success": False,
                "error": {
                    "code": exc.status_code,
                    "message": exc.detail,
                },
                "meta": {
                    "path": str(request.url.path),
                    "method": request.method,
                }
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """Handle unexpected exceptions."""
        logger.error(
            "Unexpected exception occurred",
            error=str(exc),
            path=request.url.path,
            exc_info=True
        )
        
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": {
                    "code": 500,
                    "message": "Internal server error",
                },
                "meta": {
                    "path": str(request.url.path),
                    "method": request.method,
                }
            }
        )

# Create the app instance
app = create_app()

# Health check endpoint
@app.get("/health")
async def health_check():
    """Comprehensive health check endpoint."""
    health_status = {
        "success": True,
        "status": "healthy",
        "version": "1.0.0",
        "environment": settings.env,
        "mock_mode": settings.mock.use_mock_data,
        "services": {}
    }
    
    # Check database health if not in mock mode
    if not settings.mock.use_mock_data:
        try:
            db = await get_database()
            db_health = await db.health_check()
            health_status["services"]["database"] = db_health
            
            if not all(db_health.values()):
                health_status["status"] = "degraded"
                
        except Exception as e:
            health_status["services"]["database"] = {"error": str(e)}
            health_status["status"] = "degraded"
    else:
        health_status["services"]["database"] = {"status": "mocked"}
    
    # Check Redis health if not in mock mode
    if not settings.mock.use_mock_redis:
        try:
            redis = await get_redis()
            redis_health = await redis.health_check()
            health_status["services"]["redis"] = redis_health
            
            if not all(value for key, value in redis_health.items() if key != "details"):
                health_status["status"] = "degraded"
                
        except Exception as e:
            health_status["services"]["redis"] = {"error": str(e)}
            health_status["status"] = "degraded"
    else:
        health_status["services"]["redis"] = {"status": "mocked"}
    
    # Overall health status
    if health_status["status"] != "healthy":
        health_status["success"] = False
    
    return health_status

# Simple test endpoint
@app.get("/test")
async def simple_test():
    """Simple test endpoint without complex dependencies."""
    return {"message": "Server is working!", "timestamp": "2025-06-25"}

# Add a runnable main block to allow for graceful shutdown
if __name__ == "__main__":
    import uvicorn
    import sys

    print("Starting Revisionary API server...")
    print("Press Ctrl+C to stop the server.")

    try:
        uvicorn.run(
            app,
            host=settings.api.host,
            port=settings.api.port,
            log_level="info",
            reload=settings.debug
        )
    except KeyboardInterrupt:
        print("\nShutting down server gracefully.")
        sys.exit(0)