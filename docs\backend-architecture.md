# Revisionary - Backend Architecture

## 1. Overview

The Revisionary backend is designed as a microservices architecture optimized for high-performance AI text processing, real-time collaboration, and cost-efficient scaling. The system leverages parallel agent processing with intelligent caching to deliver sub-2-second response times.

## 2. Technology Stack

### 2.1 Core Technologies
- **API Framework**: FastAPI 0.104+ (async Python)
- **Language**: Python 3.11+
- **Authentication**: Firebase Admin SDK
- **Message Queue**: Google Cloud Tasks
- **Caching**: Redis 7.2 (local VM instance)
- **Database**: PostgreSQL 15 (via Supabase)
- **Object Storage**: Google Cloud Storage
- **Container**: Docker + Docker Compose
- **Monitoring**: Prometheus + Grafana

### 2.2 AI/LLM Stack
- **Model Configuration**: All models defined in [`/infra/models.yaml`](/infra/models.yaml) (single source of truth)
- **Primary Models**:
  - Grammar Agent: GPT-4.1 nano (max 4k tokens, $0.01/1k input)
  - Style Agent: GPT-4.1 mini (max 8k tokens, $0.01/1k input)  
  - Structure Agent: GPT-4.1 full (max 16k tokens, $0.01/1k input)
  - Content Agent: GPT-4.1 full / Gemini 2.5 Flash (2M context, cached)
- **Cost Optimization**: 
  - Gemini 2.5 Flash: $0.000075/1k input, 75% discount for cached context
  - Smart routing based on context size and complexity
- **Fallback Strategy**: OpenAI GPT-3.5-turbo-0125 (cost-effective fallback)
- **Caching Strategy**: Aggressive context caching for all agents

## 3. System Architecture

### 3.1 Service Components

```
┌─────────────────┐     ┌──────────────┐     ┌─────────────────┐
│   API Gateway   │────▶│ Queue Layer  │────▶│ Agent Workers   │
│   (FastAPI)     │     │Budget: Redis │     │  (Containers)   │
└────────┬────────┘     │Enterprise:   │     └─────────────────┘
         │               │Cloud Tasks   │              │
         │               └──────────────┘              │
         │  ┌─────────────────────────────────────────┐│
         ├─▶│ Redis (Local)                           ││
         │  │ • Cache/Queue (Redis Streams)           ││
         │  │ • Pub-Sub (real-time events)           ││
         │  │ • 15min AOF→GCS                        ││
         │  │ • HA: 3-node Sentinel (>50k DAU)       ││
         │  └─────────────────────────────────────────┘│
         │                                             │
         │  ┌──────────────┐     ┌──────────────┐    │
         └─▶│  PostgreSQL  │     │     GCS      │◀───┘
            │  (Supabase)  │     │  (Backups)   │
            └──────────────┘     └──────────────┘
```

**Queue Strategy per Deployment Tier:**

| Tier | Queue System | Cost | Use Case |
|------|-------------|------|----------|
| **Budget** (≤$150/month) | Redis Streams | Included in VM cost | 0-10k users, simple job distribution |
| **Enterprise** (>$500/month) | Cloud Tasks | ~$40/month | 10k+ users, advanced scheduling, retries |

**Queue Layer Clarification:**
- **Redis Streams**: Job queue for worker distribution (budget tier)
- **Cloud Tasks**: Job queue for worker distribution (enterprise tier)  
- **Redis Pub/Sub**: Real-time event bus for all tiers (separate from job queues)

### 3.2 Request Flow

1. **Client Request** → API Gateway (authentication, validation)
2. **Context Building** → Fetch relevant document context from cache/DB
3. **Job Dispatch** → Enqueue parallel jobs to Cloud Tasks
4. **Agent Processing** → Workers process with specific LLM models
5. **Result Aggregation** → Combine results, handle conflicts
6. **Response Stream** → SSE/WebSocket back to client

## 4. API Design

### 4.1 Core Endpoints

```python
# Document Management
GET    /api/v1/documents                 # List documents
POST   /api/v1/documents                 # Create document
GET    /api/v1/documents/{id}           # Get document
PATCH  /api/v1/documents/{id}           # Update document
DELETE /api/v1/documents/{id}           # Delete document

# Block Operations
GET    /api/v1/blocks/{id}              # Get block with suggestions
PATCH  /api/v1/blocks/{id}              # Update block content
POST   /api/v1/blocks/{id}/suggestions  # Request AI suggestions

# AI Operations  
POST   /api/v1/ai/generate              # Generate content
POST   /api/v1/ai/rewrite               # Rewrite selection
POST   /api/v1/ai/analyze               # Analyze document
SSE    /api/v1/ai/stream/{job_id}       # Stream results

# Real-time
WS     /ws/documents/{id}               # Document collaboration
SSE    /api/v1/sse/documents/{id}       # Server-sent events
```

### 4.2 Request/Response Models

```python
from pydantic import BaseModel
from typing import List, Optional, Dict
from datetime import datetime

class DocumentCreate(BaseModel):
    title: str
    type: Literal["creative", "academic", "professional", "general"]
    content: Optional[str] = ""
    metadata: Dict[str, Any] = {}

class BlockUpdate(BaseModel):
    text: str
    cursor_pos: int
    client_hash: str
    
class AIRequest(BaseModel):
    document_id: str
    block_id: Optional[str]
    intent: Literal["generate", "rewrite", "improve", "analyze"]
    context_type: Literal["sentence", "paragraph", "section", "document"]
    agent_mask: List[Literal["grammar", "style", "structure", "content"]]
    options: Dict[str, Any] = {}

class AISuggestion(BaseModel):
    id: str
    agent: str
    severity: Literal["low", "medium", "high", "critical"]
    original_text: str
    suggested_text: str
    explanation: str
    confidence: float
    position: Dict[str, int]  # start, end offsets
    metadata: Dict[str, Any]
```

## 5. Agent System Implementation

### 5.1 Agent Worker Base Class

```python
from abc import ABC, abstractmethod
import asyncio
from typing import Dict, Any, Optional
import redis.asyncio as redis

class AgentWorker(ABC):
    def __init__(self, agent_id: str, model_config: Dict[str, Any]):
        self.agent_id = agent_id
        self.model_config = model_config
        self.redis_client = None
        self.llm_client = None
        self.cache = LRUCache(maxsize=1000)
        
    async def initialize(self):
        self.redis_client = await redis.create_redis_pool(
            'redis://localhost:6379',
            password=os.getenv('REDIS_PASSWORD')
        )
        self.llm_client = self._create_llm_client()
        
    @abstractmethod
    def _create_llm_client(self):
        """Create appropriate LLM client for this agent"""
        pass
        
    @abstractmethod
    async def process_job(self, job: Dict[str, Any]) -> Dict[str, Any]:
        """Process a single job and return results"""
        pass
        
    async def run(self):
        """Main worker loop"""
        while True:
            try:
                # Pull job from Redis stream
                job = await self.redis_client.xreadgroup(
                    'agents', self.agent_id, {'jobs': '>'}, count=1, block=1000
                )
                
                if job:
                    result = await self.process_job(job[0])
                    await self._publish_result(result)
                    
            except Exception as e:
                logger.error(f"Worker {self.agent_id} error: {e}")
                await asyncio.sleep(1)
```

### 5.2 Grammar Agent Implementation

```python
class GrammarAgent(AgentWorker):
    def _create_llm_client(self):
        return OpenAIClient(
            model="gpt-4-1106-preview",  # GPT-4.1 nano
            temperature=0.1,
            max_tokens=150
        )
        
    async def process_job(self, job: Dict[str, Any]) -> Dict[str, Any]:
        context = job['context']
        text = job['text']
        
        # Check cache first
        cache_key = f"grammar:{hash(text)}"
        cached = await self.redis_client.get(cache_key)
        if cached:
            return json.loads(cached)
            
        # Build prompt
        prompt = self._build_prompt(text, context)
        
        # Call LLM with retry logic
        response = await self._call_llm_with_retry(prompt)
        
        # Parse response and extract suggestions
        suggestions = self._parse_grammar_suggestions(response)
        
        # Cache result
        await self.redis_client.setex(
            cache_key, 
            3600,  # 1 hour TTL
            json.dumps(suggestions)
        )
        
        return {
            'job_id': job['id'],
            'agent': 'grammar',
            'suggestions': suggestions,
            'processing_time': time.time() - job['start_time']
        }
```

### 5.3 Context Building System

```python
class ContextBuilder:
    def __init__(self, redis_client, db_client):
        self.redis = redis_client
        self.db = db_client
        
    async def build_context(self, span: Dict, agent: str) -> str:
        """Build appropriate context for each agent type"""
        
        if agent == "grammar":
            return await self._get_sentence_context(span)
        elif agent == "style":
            return await self._get_paragraph_context(span)
        elif agent == "structure":
            return await self._get_section_context(span)
        elif agent == "content":
            return await self._get_document_context(span)
            
    async def _get_sentence_context(self, span: Dict) -> str:
        """Get current sentence ± 1 neighboring sentence"""
        block_id = span['block_id']
        
        # Try cache first
        cache_key = f"ctx:sentence:{block_id}"
        cached = await self.redis.get(cache_key)
        if cached:
            return cached
            
        # Fetch from DB
        block = await self.db.get_block(block_id)
        sentences = self._split_sentences(block.text)
        
        # Find target sentence and neighbors
        target_idx = self._find_sentence_index(sentences, span['offset'])
        start = max(0, target_idx - 1)
        end = min(len(sentences), target_idx + 2)
        
        context = ' '.join(sentences[start:end])
        
        # Cache for 30 minutes
        await self.redis.setex(cache_key, 1800, context)
        return context
```

### 5.4 Caching Strategy

```python
class CacheManager:
    """Centralized caching logic with Gemini 2.5 Flash context caching"""
    
    def __init__(self):
        self.redis = redis.AsyncRedis()
        self.gemini_cache = {}  # In-memory for Gemini context caching
        
    async def get_or_compute(self, key: str, compute_fn, ttl: int = 3600):
        """Generic cache-or-compute pattern"""
        
        # Check Redis first
        cached = await self.redis.get(key)
        if cached:
            return json.loads(cached)
            
        # Compute if not cached
        result = await compute_fn()
        
        # Store in cache
        await self.redis.setex(key, ttl, json.dumps(result))
        return result
        
    async def cache_gemini_context(self, doc_id: str, context: str):
        """Special handling for Gemini 2.5 Flash context caching"""
        
        # Gemini supports caching large contexts for reuse
        cache_token = await self.gemini_client.cache_context(
            context=context,
            cache_duration=3600  # 1 hour
        )
        
        self.gemini_cache[doc_id] = {
            'token': cache_token,
            'expires': time.time() + 3600
        }
        
        # Also store in Redis for persistence
        await self.redis.setex(
            f"gemini_cache:{doc_id}",
            3600,
            cache_token
        )
```

## 6. Database Schema

### 6.1 Core Tables

```sql
-- Users table (managed by Firebase, mirrored here)
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    firebase_uid VARCHAR(128) UNIQUE NOT NULL,
    email VARCHAR(255) NOT NULL,
    display_name VARCHAR(255),
    subscription_tier VARCHAR(50) DEFAULT 'free',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Documents table
CREATE TABLE documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    owner_id UUID REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(500) NOT NULL,
    type VARCHAR(50) NOT NULL, -- creative, academic, professional, general
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    word_count INTEGER DEFAULT 0,
    version INTEGER DEFAULT 1,
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Blocks table (hierarchical document structure)
CREATE TABLE blocks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID REFERENCES documents(id) ON DELETE CASCADE,
    parent_id UUID REFERENCES blocks(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL, -- chapter, section, paragraph
    position INTEGER NOT NULL,
    content TEXT,
    hash VARCHAR(64), -- SHA256 of content
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Versions table (document history)
CREATE TABLE versions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    block_id UUID REFERENCES blocks(id) ON DELETE CASCADE,
    version_number INTEGER NOT NULL,
    content TEXT NOT NULL,
    author_id UUID REFERENCES users(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    change_summary TEXT,
    UNIQUE(block_id, version_number)
);

-- Suggestions table (AI suggestions)
CREATE TABLE suggestions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    block_id UUID REFERENCES blocks(id) ON DELETE CASCADE,
    agent_type VARCHAR(50) NOT NULL,
    severity VARCHAR(20) NOT NULL,
    original_text TEXT NOT NULL,
    suggested_text TEXT NOT NULL,
    explanation TEXT,
    confidence FLOAT,
    position JSONB NOT NULL, -- {start: int, end: int}
    status VARCHAR(20) DEFAULT 'pending', -- pending, accepted, rejected, stale
    created_at TIMESTAMPTZ DEFAULT NOW(),
    resolved_at TIMESTAMPTZ,
    resolved_by UUID REFERENCES users(id)
);

-- Indexes for performance
CREATE INDEX idx_blocks_document_position ON blocks(document_id, position);
CREATE INDEX idx_blocks_parent ON blocks(parent_id);
CREATE INDEX idx_suggestions_block_status ON suggestions(block_id, status);
CREATE INDEX idx_documents_owner ON documents(owner_id);

-- Full-text search
CREATE INDEX idx_blocks_content_fts ON blocks USING gin(to_tsvector('english', content));
```

### 6.2 Summaries and Embeddings

```sql
-- Summaries table (cached AI summaries)
CREATE TABLE summaries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    block_id UUID REFERENCES blocks(id) ON DELETE CASCADE,
    level VARCHAR(20) NOT NULL, -- paragraph, section, document
    summary_text TEXT NOT NULL,
    embedding vector(1536), -- For semantic search
    token_count INTEGER,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ,
    UNIQUE(block_id, level)
);

-- Vector similarity search
CREATE INDEX idx_summaries_embedding ON summaries 
USING ivfflat (embedding vector_cosine_ops)
WITH (lists = 100);
```

## 7. Queue and Messaging

### 7.1 Cloud Tasks Configuration

```python
from google.cloud import tasks_v2
from google.protobuf import timestamp_pb2
import datetime

class TaskQueue:
    def __init__(self, project_id: str, location: str):
        self.client = tasks_v2.CloudTasksClient()
        self.project_id = project_id
        self.location = location
        self.queues = {
            'grammar': f"projects/{project_id}/locations/{location}/queues/grammar-queue",
            'style': f"projects/{project_id}/locations/{location}/queues/style-queue",
            'structure': f"projects/{project_id}/locations/{location}/queues/structure-queue",
            'content': f"projects/{project_id}/locations/{location}/queues/content-queue",
        }
        
    async def enqueue_job(self, agent: str, payload: Dict[str, Any]):
        """Enqueue a job for specific agent"""
        
        task = {
            'http_request': {
                'http_method': tasks_v2.HttpMethod.POST,
                'url': f"https://worker-{agent}.revisionary.app/process",
                'headers': {'Content-Type': 'application/json'},
                'body': json.dumps(payload).encode(),
            }
        }
        
        # Set execution time (immediate)
        timestamp = timestamp_pb2.Timestamp()
        timestamp.FromDatetime(datetime.datetime.utcnow())
        task['schedule_time'] = timestamp
        
        # Create task
        parent = self.queues[agent]
        response = self.client.create_task(parent=parent, task=task)
        
        return response.name
```

### 7.2 Redis Pub/Sub for Real-time

```python
class RealtimePublisher:
    def __init__(self, redis_client):
        self.redis = redis_client
        self.channels = {
            'suggestions': 'channel:suggestions:{}',  # document_id
            'updates': 'channel:updates:{}',          # document_id
            'presence': 'channel:presence:{}',        # document_id
        }
        
    async def publish_suggestion(self, doc_id: str, suggestion: Dict):
        """Publish new suggestion to document channel"""
        channel = self.channels['suggestions'].format(doc_id)
        await self.redis.publish(channel, json.dumps({
            'type': 'suggestion:new',
            'data': suggestion,
            'timestamp': time.time()
        }))
        
    async def publish_stale_notification(self, doc_id: str, suggestion_id: str):
        """Notify that a suggestion is now stale"""
        channel = self.channels['suggestions'].format(doc_id)
        await self.redis.publish(channel, json.dumps({
            'type': 'suggestion:stale',
            'suggestion_id': suggestion_id,
            'timestamp': time.time()
        }))
```

## 8. Security Implementation

### 8.1 Authentication Middleware

```python
from firebase_admin import auth
from fastapi import HTTPException, Depends
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials

security = HTTPBearer()

async def verify_firebase_token(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> Dict[str, Any]:
    """Verify Firebase ID token and return user claims"""
    
    token = credentials.credentials
    
    try:
        # Verify token with Firebase Admin SDK
        decoded_token = auth.verify_id_token(token, check_revoked=True)
        
        # Check if user exists in our database
        user = await get_user_by_firebase_uid(decoded_token['uid'])
        if not user:
            # Create user if first time
            user = await create_user_from_firebase(decoded_token)
            
        return {
            'user_id': user.id,
            'firebase_uid': decoded_token['uid'],
            'email': decoded_token.get('email'),
            'subscription_tier': user.subscription_tier
        }
        
    except auth.InvalidIdTokenError:
        raise HTTPException(status_code=401, detail="Invalid authentication token")
    except auth.ExpiredIdTokenError:
        raise HTTPException(status_code=401, detail="Token has expired")
    except Exception as e:
        logger.error(f"Auth error: {e}")
        raise HTTPException(status_code=401, detail="Authentication failed")
```

### 8.2 Rate Limiting

```python
from typing import Callable
import time

class RateLimiter:
    def __init__(self, redis_client):
        self.redis = redis_client
        self.limits = {
            'free': {'requests': 100, 'window': 3600},      # 100/hour
            'professional': {'requests': 1000, 'window': 3600}, # 1000/hour
            'studio': {'requests': 10000, 'window': 3600},   # 10000/hour
            'enterprise': {'requests': -1, 'window': 3600},  # Unlimited
        }
        
    async def check_rate_limit(self, user_id: str, tier: str) -> bool:
        """Check if user has exceeded rate limit"""
        
        if tier == 'enterprise':
            return True
            
        limit_config = self.limits.get(tier, self.limits['free'])
        key = f"rate_limit:{user_id}"
        
        current = await self.redis.incr(key)
        
        if current == 1:
            # First request, set expiry
            await self.redis.expire(key, limit_config['window'])
            
        return current <= limit_config['requests']
```

### 8.3 Token Usage Tracking

```python
class TokenUsageTracker:
    def __init__(self, redis_client):
        self.redis = redis_client
        self.monthly_limits = {
            'free': 10_000,
            'professional': 225_000,
            'studio': 1_000_000,
            'enterprise': -1  # Unlimited
        }
        
    async def track_usage(self, user_id: str, tokens: int, model: str):
        """Track token usage for billing"""
        
        # Daily counter
        daily_key = f"tokens:daily:{user_id}:{datetime.now().strftime('%Y%m%d')}"
        await self.redis.incrby(daily_key, tokens)
        await self.redis.expire(daily_key, 86400 * 7)  # Keep for 7 days
        
        # Monthly counter
        monthly_key = f"tokens:monthly:{user_id}:{datetime.now().strftime('%Y%m')}"
        new_total = await self.redis.incrby(monthly_key, tokens)
        
        # Model-specific tracking
        model_key = f"tokens:model:{user_id}:{model}:{datetime.now().strftime('%Y%m')}"
        await self.redis.incrby(model_key, tokens)
        
        return new_total
        
    async def check_limit(self, user_id: str, tier: str) -> tuple[bool, int]:
        """Check if user is within monthly limit"""
        
        limit = self.monthly_limits.get(tier, self.monthly_limits['free'])
        if limit == -1:
            return True, -1
            
        monthly_key = f"tokens:monthly:{user_id}:{datetime.now().strftime('%Y%m')}"
        current = int(await self.redis.get(monthly_key) or 0)
        
        return current < limit, limit - current
```

## 9. Performance Optimization

### 9.1 Connection Pooling

```python
from contextlib import asynccontextmanager
import asyncpg
import redis.asyncio as redis

class ConnectionManager:
    def __init__(self):
        self.pg_pool = None
        self.redis_pool = None
        
    async def initialize(self):
        # PostgreSQL connection pool
        self.pg_pool = await asyncpg.create_pool(
            dsn=os.getenv('DATABASE_URL'),
            min_size=10,
            max_size=20,
            max_queries=50000,
            max_inactive_connection_lifetime=300
        )
        
        # Redis connection pool
        self.redis_pool = redis.ConnectionPool(
            host='localhost',
            port=6379,
            password=os.getenv('REDIS_PASSWORD'),
            max_connections=50,
            decode_responses=True
        )
        
    @asynccontextmanager
    async def get_db(self):
        async with self.pg_pool.acquire() as connection:
            yield connection
            
    def get_redis(self):
        return redis.Redis(connection_pool=self.redis_pool)
```

### 9.2 Batch Processing

```python
class BatchProcessor:
    """Process multiple blocks in a single LLM call for efficiency"""
    
    def __init__(self, llm_client):
        self.llm_client = llm_client
        self.batch_size = 5
        self.max_wait_time = 100  # ms
        
    async def process_batch(self, jobs: List[Dict]) -> List[Dict]:
        """Process multiple jobs in one LLM call"""
        
        # Build combined prompt
        combined_prompt = self._build_batch_prompt(jobs)
        
        # Single LLM call for all jobs
        response = await self.llm_client.complete(
            prompt=combined_prompt,
            max_tokens=len(jobs) * 200
        )
        
        # Parse response back to individual results
        results = self._parse_batch_response(response, jobs)
        
        return results
```

## 10. Monitoring and Observability

### 10.1 Metrics Collection

```python
from prometheus_client import Counter, Histogram, Gauge
import time

# Define metrics
request_count = Counter('api_requests_total', 'Total API requests', ['method', 'endpoint', 'status'])
request_duration = Histogram('api_request_duration_seconds', 'API request duration', ['method', 'endpoint'])
active_connections = Gauge('websocket_connections_active', 'Active WebSocket connections')
token_usage = Counter('llm_tokens_used_total', 'Total LLM tokens used', ['model', 'agent'])
cache_hits = Counter('cache_hits_total', 'Cache hit count', ['cache_type'])
cache_misses = Counter('cache_misses_total', 'Cache miss count', ['cache_type'])

# Middleware for metrics
@app.middleware("http")
async def metrics_middleware(request: Request, call_next):
    start_time = time.time()
    
    response = await call_next(request)
    
    duration = time.time() - start_time
    request_count.labels(
        method=request.method,
        endpoint=request.url.path,
        status=response.status_code
    ).inc()
    
    request_duration.labels(
        method=request.method,
        endpoint=request.url.path
    ).observe(duration)
    
    return response
```

### 10.2 Logging Configuration

```python
import structlog
from pythonjsonlogger import jsonlogger

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()
```

## 11. Deployment Configuration

### 11.1 Docker Configuration

```dockerfile
# Base image for all services
FROM python:3.11-slim as base

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# API Gateway
FROM base as api-gateway
COPY api/ ./api/
COPY common/ ./common/
CMD ["uvicorn", "api.main:app", "--host", "0.0.0.0", "--port", "8000"]

# Agent Worker
FROM base as agent-worker
COPY workers/ ./workers/
COPY common/ ./common/
ARG AGENT_TYPE
ENV AGENT_TYPE=${AGENT_TYPE}
CMD ["python", "-m", "workers.main", "--agent", "${AGENT_TYPE}"]
```

### 11.2 Environment Configuration

```bash
# .env.production
ENVIRONMENT=production
DEBUG=false

# Firebase
FIREBASE_PROJECT_ID=revisionary-prod
FIREBASE_PRIVATE_KEY_ID=xxx
FIREBASE_PRIVATE_KEY=xxx
FIREBASE_CLIENT_EMAIL=xxx

# Database
DATABASE_URL=********************************/revisionary
REDIS_URL=redis://:password@localhost:6379/0

# LLM API Keys
OPENAI_API_KEY=sk-xxx
GOOGLE_AI_API_KEY=xxx

# Monitoring
SENTRY_DSN=https://<EMAIL>/xxx
PROMETHEUS_PORT=9090

# Limits
MAX_DOCUMENT_SIZE_MB=10
MAX_CONCURRENT_JOBS=100
TOKEN_BUDGET_MONTHLY=50  # USD
```

## 12. Error Handling and Recovery

### 12.1 Retry Logic

```python
from tenacity import retry, stop_after_attempt, wait_exponential
import httpx

class RetryableAPIClient:
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=2, max=10)
    )
    async def call_llm(self, prompt: str, model: str, **kwargs):
        """Call LLM with automatic retry and fallback"""
        
        try:
            if model.startswith('gpt'):
                response = await self.openai_client.complete(prompt=prompt, model=model, **kwargs)
            elif model.startswith('gemini'):
                response = await self.google_client.complete(prompt=prompt, model=model, **kwargs)
            else:
                raise ValueError(f"Unsupported model: {model}")
                
            return response
        except httpx.HTTPStatusError as e:
            if e.response.status_code in [429, 500, 502, 503]:
                # Retryable errors
                logger.warning(f"Retryable error for {model}: {e}")
                raise
            else:
                # Non-retryable errors
                logger.error(f"Non-retryable error for {model}: {e}")
                raise
        except Exception as e:
            logger.error(f"Unexpected error for {model}: {e}")
            raise
```

### 12.2 Circuit Breaker

```python
from circuitbreaker import circuit

class LLMService:
    @circuit(failure_threshold=5, recovery_timeout=30)
    async def generate(self, prompt: str, model: str):
        """Generate with circuit breaker protection"""
        
        try:
            if model.startswith('gpt'):
                return await self.openai_client.complete(prompt, model=model)
            elif model.startswith('gemini'):
                return await self.google_client.complete(prompt, model=model)
            else:
                raise ValueError(f"Unsupported model: {model}")
        except Exception as e:
            logger.error(f"LLM service error for {model}: {e}")
            raise
```

## 13. Redis High Availability Upgrade

**When traffic exceeds 50,000 daily active users**, promote to three-node Redis Sentinel:

### 13.1 Sentinel Configuration
```bash
# Add two Redis replica VMs
gcloud compute instances create redis-replica-1 redis-replica-2 \
  --machine-type=e2-medium \
  --zone=us-central1-a \
  --image-family=ubuntu-2204-lts

# Configure replicas
echo "replicaof MASTER_IP 6379" >> /etc/redis/redis.conf
echo "masterauth PASSWORD" >> /etc/redis/redis.conf

# Setup Sentinel on all 3 nodes
cat > /etc/redis/sentinel.conf << 'EOF'
port 26379
sentinel monitor revisionary-master MASTER_IP 6379 2
sentinel auth-pass revisionary-master PASSWORD  
sentinel down-after-milliseconds revisionary-master 5000
sentinel failover-timeout revisionary-master 60000
sentinel parallel-syncs revisionary-master 1
EOF

# Start Sentinel services
systemctl enable redis-sentinel
systemctl start redis-sentinel
```

### 13.2 Application Connection Update
```python
import redis.sentinel

# Update Redis client for Sentinel
sentinels = [
    ('sentinel-1', 26379),
    ('sentinel-2', 26379), 
    ('sentinel-3', 26379)
]

sentinel = redis.sentinel.Sentinel(sentinels)
redis_client = sentinel.master_for('revisionary-master', socket_timeout=0.1)
```

This backend architecture provides a robust, scalable foundation for the Revisionary AI writing assistant with careful attention to performance, reliability, and cost optimization.