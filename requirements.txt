# Revisionary Python Dependencies
# Core framework
fastapi[all]>=0.104.0
uvicorn[standard]>=0.24.0

# Data models and validation
pydantic[email]>=2.5.0

# Database
sqlalchemy>=2.0.0
alembic>=1.13.0
asyncpg>=0.29.0

# Caching and messaging
redis[hiredis]>=5.0.0

# AI/LLM services
openai>=1.3.0
google-cloud-aiplatform>=1.38.0

# Authentication
firebase-admin>=6.2.0

# HTTP and security
python-multipart>=0.0.6
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
httpx>=0.25.0

# WebSocket and real-time
websockets>=12.0
python-socketio>=5.10.0

# Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0

# Code quality
black>=23.9.0
ruff>=0.1.0
mypy>=1.6.0

# Logging and monitoring
structlog>=23.2.0
prometheus-client>=0.19.0
sentry-sdk[fastapi]>=1.38.0

# Reliability
tenacity>=8.2.0
circuitbreaker>=1.4.0