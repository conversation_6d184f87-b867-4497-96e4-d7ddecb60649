{"name": "revisionary-frontend", "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "npx vite build", "build:quick": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:e2e": "playwright test", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@tanstack/react-query": "^5.8.0", "clsx": "^2.0.0", "firebase": "^10.5.0", "framer-motion": "^10.16.0", "jotai": "^2.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hotkeys-hook": "^4.4.0", "react-router-dom": "^6.17.0", "react-window": "^1.8.8", "react-window-infinite-loader": "^1.0.9", "socket.io-client": "^4.7.0", "zustand": "^4.4.0"}, "devDependencies": {"@playwright/test": "^1.39.0", "@tanstack/react-query-devtools": "^5.80.7", "@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/react-window": "^1.8.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.0", "@vitest/ui": "^0.34.0", "autoprefixer": "^10.4.0", "eslint": "^8.45.0", "eslint-config-airbnb": "^19.0.0", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.0", "postcss": "^8.4.0", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.5.0", "tailwindcss": "^3.3.0", "terser": "^5.43.0", "typescript": "^5.0.0", "vite": "^4.4.0", "vitest": "^0.34.0"}}