#!/usr/bin/env python3
"""
SQL Duplicate Checker for Revisionary Mock Data

This script analyzes all SQL files in the mock_data directory to identify
potential duplicate data that could cause "query returned more than one row" errors.
"""

import re
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Set

def extract_insert_values(sql_content: str, table_name: str) -> List[Dict]:
    """Extract INSERT values for a specific table from SQL content."""
    inserts = []
    
    # Pattern to match INSERT INTO statements for the specific table
    pattern = rf"INSERT INTO {table_name}\s*\([^)]+\)\s*VALUES\s*"
    
    matches = re.finditer(pattern, sql_content, re.IGNORECASE | re.MULTILINE)
    
    for match in matches:
        # Find the VALUES part
        start_pos = match.end()
        # Look for the values in parentheses
        paren_count = 0
        values_start = None
        
        i = start_pos
        while i < len(sql_content):
            char = sql_content[i]
            if char == '(':
                if values_start is None:
                    values_start = i
                paren_count += 1
            elif char == ')':
                paren_count -= 1
                if paren_count == 0:
                    # Found complete values clause
                    values_text = sql_content[values_start:i+1]
                    inserts.append({
                        'values': values_text,
                        'position': match.start()
                    })
                    break
            elif char == ';' and paren_count == 0:
                break
            i += 1
    
    return inserts

def extract_firebase_uids(sql_content: str) -> List[str]:
    """Extract firebase_uid values from SQL content."""
    pattern = r"firebase_uid\s*=\s*'([^']+)'"
    matches = re.findall(pattern, sql_content, re.IGNORECASE)
    return matches

def extract_document_titles(sql_content: str) -> List[str]:
    """Extract document title values from SQL content."""
    # Look for document titles in INSERT statements
    pattern = r"INSERT INTO documents.*?'([^']+)'.*?'([^']*?)'.*?'([^']*?)'"
    matches = re.findall(pattern, sql_content, re.IGNORECASE | re.DOTALL)
    titles = []
    for match in matches:
        # Usually the first quoted string after user_id is the title
        for item in match:
            if len(item) > 10 and not item.startswith('firebase_'):  # Likely a title
                titles.append(item)
                break
    return titles

def extract_persona_names(sql_content: str) -> List[str]:
    """Extract persona names from SQL content."""
    pattern = r"INSERT INTO personas.*?'([^']+)'.*?'([^']+)'"
    matches = re.findall(pattern, sql_content, re.IGNORECASE | re.DOTALL)
    names = []
    for match in matches:
        # The second quoted string is usually the name
        if len(match) > 1:
            names.append(match[1])
    return names

def extract_agent_names(sql_content: str) -> List[str]:
    """Extract custom agent names from SQL content."""
    pattern = r"INSERT INTO custom_agents.*?'([^']+)'.*?'([^']+)'"
    matches = re.findall(pattern, sql_content, re.IGNORECASE | re.DOTALL)
    names = []
    for match in matches:
        # The second quoted string is usually the name
        if len(match) > 1:
            names.append(match[1])
    return names

def find_problematic_selects(sql_content: str, filename: str) -> List[Dict]:
    """Find SELECT queries that might return multiple rows."""
    problems = []
    
    # Pattern for SELECT queries without LIMIT 1
    select_pattern = r"SELECT\s+.*?\s+FROM\s+\w+\s+WHERE\s+[^;]*?(?!.*LIMIT\s+1)[^;]*;"
    
    matches = re.finditer(select_pattern, sql_content, re.IGNORECASE | re.DOTALL)
    
    for match in matches:
        query = match.group(0)
        # Skip if it has LIMIT 1
        if 'LIMIT 1' not in query.upper():
            problems.append({
                'file': filename,
                'query': query.strip(),
                'position': match.start(),
                'issue': 'SELECT without LIMIT 1'
            })
    
    return problems

def analyze_sql_files():
    """Analyze all SQL files for potential duplicate issues."""
    mock_data_dir = Path("/mnt/c/Users/<USER>/revisionary/backend/mock_data")
    
    all_firebase_uids = defaultdict(list)
    all_document_titles = defaultdict(list)
    all_persona_names = defaultdict(list)
    all_agent_names = defaultdict(list)
    all_problems = []
    
    sql_files = sorted(mock_data_dir.glob("*.sql"))
    
    print("🔍 Analyzing SQL files for duplicates and issues...")
    print("=" * 60)
    
    for sql_file in sql_files:
        print(f"\n📄 Analyzing {sql_file.name}...")
        
        with open(sql_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extract various identifiers
        firebase_uids = extract_firebase_uids(content)
        document_titles = extract_document_titles(content)
        persona_names = extract_persona_names(content)
        agent_names = extract_agent_names(content)
        
        # Track where each identifier appears
        for uid in firebase_uids:
            all_firebase_uids[uid].append(sql_file.name)
        
        for title in document_titles:
            all_document_titles[title].append(sql_file.name)
            
        for name in persona_names:
            all_persona_names[name].append(sql_file.name)
            
        for name in agent_names:
            all_agent_names[name].append(sql_file.name)
        
        # Find problematic SELECT queries
        problems = find_problematic_selects(content, sql_file.name)
        all_problems.extend(problems)
        
        print(f"  Firebase UIDs: {len(firebase_uids)}")
        print(f"  Document titles: {len(document_titles)}")
        print(f"  Persona names: {len(persona_names)}")
        print(f"  Agent names: {len(agent_names)}")
        print(f"  Potential SELECT issues: {len(problems)}")
    
    print("\n" + "=" * 60)
    print("🚨 DUPLICATE ANALYSIS RESULTS")
    print("=" * 60)
    
    # Check for duplicates
    duplicates_found = False
    
    print("\n📋 Firebase UID Duplicates:")
    for uid, files in all_firebase_uids.items():
        if len(files) > 1:
            print(f"  ❌ '{uid}' appears in: {', '.join(files)}")
            duplicates_found = True
    
    print("\n📋 Document Title Duplicates:")
    for title, files in all_document_titles.items():
        if len(files) > 1:
            print(f"  ❌ '{title}' appears in: {', '.join(files)}")
            duplicates_found = True
    
    print("\n📋 Persona Name Duplicates:")
    for name, files in all_persona_names.items():
        if len(files) > 1:
            print(f"  ❌ '{name}' appears in: {', '.join(files)}")
            duplicates_found = True
    
    print("\n📋 Agent Name Duplicates:")
    for name, files in all_agent_names.items():
        if len(files) > 1:
            print(f"  ❌ '{name}' appears in: {', '.join(files)}")
            duplicates_found = True
    
    if not duplicates_found:
        print("✅ No obvious duplicates found in INSERT statements")
    
    print("\n📋 Potentially Problematic SELECT Queries:")
    if all_problems:
        for problem in all_problems:
            print(f"  ❌ {problem['file']}: {problem['issue']}")
            print(f"     Query: {problem['query'][:100]}...")
    else:
        print("✅ No obvious SELECT query issues found")
    
    print("\n" + "=" * 60)
    print("🔧 RECOMMENDATIONS")
    print("=" * 60)
    
    if duplicates_found:
        print("1. Remove duplicate INSERT statements")
        print("2. Ensure unique identifiers across files")
    
    if all_problems:
        print("3. Add LIMIT 1 to SELECT queries that should return single rows")
        print("4. Add proper WHERE constraints to make queries more specific")
    
    print("5. Focus investigation on 005_missing_features.sql variable assignments")
    print("6. Check for SELECT queries in DO blocks that lack proper constraints")

if __name__ == "__main__":
    analyze_sql_files()