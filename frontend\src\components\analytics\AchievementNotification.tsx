import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { XMarkIcon, TrophyIcon, StarIcon } from '@heroicons/react/24/outline';
import { Achievement } from '../../types/analytics';

interface AchievementNotificationProps {
  achievement: Achievement | null;
  onDismiss: () => void;
  duration?: number; // milliseconds
}

const AchievementNotification: React.FC<AchievementNotificationProps> = ({
  achievement,
  onDismiss,
  duration = 5000
}) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (achievement) {
      setIsVisible(true);
      
      const timer = setTimeout(() => {
        setIsVisible(false);
        setTimeout(onDismiss, 300); // Wait for exit animation
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [achievement, duration, onDismiss]);

  if (!achievement) return null;

  const rarityConfig = {
    common: {
      gradient: 'from-slate-500 to-slate-600',
      glow: 'shadow-slate-500/25',
      particles: 'text-slate-400',
      icon: StarIcon,
    },
    rare: {
      gradient: 'from-blue-500 to-blue-600',
      glow: 'shadow-blue-500/25',
      particles: 'text-blue-400',
      icon: StarIcon,
    },
    epic: {
      gradient: 'from-purple-500 to-purple-600',
      glow: 'shadow-purple-500/25',
      particles: 'text-purple-400',
      icon: TrophyIcon,
    },
    legendary: {
      gradient: 'from-yellow-500 to-yellow-600',
      glow: 'shadow-yellow-500/25',
      particles: 'text-yellow-400',
      icon: TrophyIcon,
    },
  };

  const config = rarityConfig[achievement.rarity];
  const IconComponent = config.icon;

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          className="fixed top-4 right-4 z-50"
          initial={{ opacity: 0, scale: 0.8, x: 100 }}
          animate={{ opacity: 1, scale: 1, x: 0 }}
          exit={{ opacity: 0, scale: 0.8, x: 100 }}
          transition={{ type: "spring", stiffness: 300, damping: 30 }}
        >
          <div className={`relative bg-white rounded-2xl border-2 border-transparent bg-gradient-to-r ${config.gradient} p-[2px] shadow-2xl ${config.glow}`}>
            <div className="bg-white rounded-xl p-6 relative overflow-hidden">
              {/* Background particles animation */}
              <div className="absolute inset-0 overflow-hidden">
                {[...Array(12)].map((_, i) => (
                  <motion.div
                    key={i}
                    className={`absolute w-1 h-1 ${config.particles} rounded-full`}
                    initial={{
                      x: Math.random() * 300,
                      y: Math.random() * 200,
                      opacity: 0,
                      scale: 0,
                    }}
                    animate={{
                      y: [null, -20, -40],
                      opacity: [0, 1, 0],
                      scale: [0, 1, 0],
                    }}
                    transition={{
                      duration: 2,
                      delay: i * 0.1,
                      repeat: Infinity,
                      repeatDelay: 3,
                    }}
                  />
                ))}
              </div>

              {/* Header */}
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <motion.div
                    className={`p-2 rounded-lg bg-gradient-to-r ${config.gradient} text-white`}
                    animate={{ rotate: [0, 5, -5, 0] }}
                    transition={{ duration: 0.5, delay: 0.5 }}
                  >
                    <IconComponent className="w-5 h-5" />
                  </motion.div>
                  <div>
                    <div className="text-sm font-semibold text-slate-600 uppercase tracking-wide">
                      Achievement Unlocked!
                    </div>
                    <div className={`text-xs font-medium capitalize ${config.gradient.replace('from-', 'text-').replace(' to-slate-600', '').replace(' to-blue-600', '').replace(' to-purple-600', '').replace(' to-yellow-600', '')}`}>
                      {achievement.rarity}
                    </div>
                  </div>
                </div>
                <button
                  onClick={() => {
                    setIsVisible(false);
                    setTimeout(onDismiss, 300);
                  }}
                  className="text-slate-400 hover:text-slate-600 transition-colors"
                >
                  <XMarkIcon className="w-5 h-5" />
                </button>
              </div>

              {/* Achievement details */}
              <div className="flex items-start space-x-4">
                <motion.div
                  className="text-4xl"
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                >
                  {achievement.icon}
                </motion.div>
                <div className="flex-1">
                  <motion.h3
                    className="text-lg font-bold text-slate-900 mb-1"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                  >
                    {achievement.title}
                  </motion.h3>
                  <motion.p
                    className="text-sm text-slate-600 leading-relaxed"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4 }}
                  >
                    {achievement.description}
                  </motion.p>
                  
                  {/* Progress indicator for progress-based achievements */}
                  {achievement.progress !== undefined && (
                    <motion.div
                      className="mt-3"
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.5 }}
                    >
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-xs text-slate-500">Progress</span>
                        <span className="text-xs font-medium text-slate-700">
                          {achievement.current?.toLocaleString()}/{achievement.target?.toLocaleString()}
                        </span>
                      </div>
                      <div className="w-full bg-slate-200 rounded-full h-2">
                        <motion.div
                          className={`h-2 rounded-full bg-gradient-to-r ${config.gradient}`}
                          initial={{ width: 0 }}
                          animate={{ width: `${achievement.progress}%` }}
                          transition={{ duration: 1, delay: 0.6 }}
                        />
                      </div>
                    </motion.div>
                  )}
                </div>
              </div>

              {/* Glow effect */}
              <motion.div
                className={`absolute inset-0 bg-gradient-to-r ${config.gradient} opacity-5 rounded-xl`}
                animate={{ opacity: [0.05, 0.1, 0.05] }}
                transition={{ duration: 2, repeat: Infinity }}
              />
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default AchievementNotification;