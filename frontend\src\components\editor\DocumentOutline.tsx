import React, { useState, useMemo } from 'react';
import {
  DocumentTextIcon,
  ChevronRightIcon,
  ChevronDownIcon,
  HashtagIcon,
  ListBulletIcon,
  ChatBubbleLeftIcon,
} from '@heroicons/react/24/outline';
import { motion, AnimatePresence } from 'framer-motion';

interface OutlineItem {
  id: string;
  level: number;
  text: string;
  line: number;
  type: 'heading' | 'paragraph' | 'list' | 'quote';
}

interface DocumentOutlineProps {
  content: string;
  onNavigate: (line: number) => void;
}

const DocumentOutline: React.FC<DocumentOutlineProps> = ({ content, onNavigate }) => {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['headings']));

  const outline = useMemo(() => {
    const lines = content.split('\n');
    const items: OutlineItem[] = [];
    
    lines.forEach((line, index) => {
      const trimmed = line.trim();
      
      if (trimmed.startsWith('#')) {
        // Headers
        const level = (trimmed.match(/^#+/) || [''])[0].length;
        const text = trimmed.replace(/^#+\s*/, '');
        if (text) {
          items.push({
            id: `heading-${index}`,
            level,
            text,
            line: index + 1,
            type: 'heading',
          });
        }
      } else if (trimmed.startsWith('-') || trimmed.startsWith('*') || trimmed.startsWith('+')) {
        // List items
        const text = trimmed.replace(/^[-*+]\s*/, '');
        if (text) {
          items.push({
            id: `list-${index}`,
            level: 1,
            text,
            line: index + 1,
            type: 'list',
          });
        }
      } else if (trimmed.startsWith('>')) {
        // Quotes
        const text = trimmed.replace(/^>\s*/, '');
        if (text) {
          items.push({
            id: `quote-${index}`,
            level: 1,
            text,
            line: index + 1,
            type: 'quote',
          });
        }
      } else if (trimmed.length > 0 && !trimmed.startsWith('#')) {
        // Regular paragraphs (show first few significant ones)
        if (trimmed.length > 20 && items.filter(item => item.type === 'paragraph').length < 5) {
          items.push({
            id: `paragraph-${index}`,
            level: 1,
            text: trimmed.length > 60 ? `${trimmed.substring(0, 60)}...` : trimmed,
            line: index + 1,
            type: 'paragraph',
          });
        }
      }
    });

    return items;
  }, [content]);

  const groupedOutline = useMemo(() => {
    const headings = outline.filter(item => item.type === 'heading');
    const lists = outline.filter(item => item.type === 'list');
    const quotes = outline.filter(item => item.type === 'quote');
    const paragraphs = outline.filter(item => item.type === 'paragraph');

    return {
      headings,
      lists,
      quotes,
      paragraphs,
    };
  }, [outline]);

  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(section)) {
      newExpanded.delete(section);
    } else {
      newExpanded.add(section);
    }
    setExpandedSections(newExpanded);
  };

  const getIcon = (type: string) => {
    switch (type) {
      case 'heading':
        return HashtagIcon;
      case 'list':
        return ListBulletIcon;
      case 'quote':
        return ChatBubbleLeftIcon;
      default:
        return DocumentTextIcon;
    }
  };

  const sections = [
    { key: 'headings', label: 'Headings', items: groupedOutline.headings, color: 'text-purple-600' },
    { key: 'lists', label: 'Lists', items: groupedOutline.lists, color: 'text-blue-600' },
    { key: 'quotes', label: 'Quotes', items: groupedOutline.quotes, color: 'text-green-600' },
    { key: 'paragraphs', label: 'Key Paragraphs', items: groupedOutline.paragraphs, color: 'text-slate-600' },
  ];

  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2 mb-4">
        <DocumentTextIcon className="w-5 h-5 text-purple-600" />
        <h3 className="text-sm font-semibold text-slate-900">Document Outline</h3>
        <span className="text-xs text-slate-500 bg-slate-100 px-2 py-0.5 rounded-full">
          {outline.length} items
        </span>
      </div>

      {outline.length === 0 ? (
        <div className="text-center py-8">
          <DocumentTextIcon className="w-12 h-12 text-slate-300 mx-auto mb-3" />
          <p className="text-sm text-slate-500 mb-2">No outline yet</p>
          <p className="text-xs text-slate-400">
            Start writing headings, lists, or paragraphs to see the document structure.
          </p>
        </div>
      ) : (
        <div className="space-y-3">
          {sections.map((section) => {
            if (section.items.length === 0) return null;
            
            const isExpanded = expandedSections.has(section.key);
            const IconComponent = getIcon(section.key.slice(0, -1));

            return (
              <div key={section.key} className="border border-slate-200 rounded-lg overflow-hidden">
                <button
                  onClick={() => toggleSection(section.key)}
                  className="w-full flex items-center justify-between p-3 hover:bg-slate-50 transition-colors"
                >
                  <div className="flex items-center space-x-2">
                    <IconComponent className={`w-4 h-4 ${section.color}`} />
                    <span className="text-sm font-medium text-slate-700">{section.label}</span>
                    <span className="text-xs text-slate-500 bg-slate-100 px-1.5 py-0.5 rounded">
                      {section.items.length}
                    </span>
                  </div>
                  <motion.div
                    animate={{ rotate: isExpanded ? 90 : 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <ChevronRightIcon className="w-4 h-4 text-slate-400" />
                  </motion.div>
                </button>

                <AnimatePresence>
                  {isExpanded && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.2 }}
                      className="border-t border-slate-200"
                    >
                      <div className="p-2 space-y-1">
                        {section.items.map((item, index) => (
                          <motion.button
                            key={item.id}
                            onClick={() => onNavigate(item.line)}
                            className="w-full text-left p-2 hover:bg-slate-50 rounded-lg transition-colors group"
                            initial={{ opacity: 0, x: -10 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: index * 0.05 }}
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex-1 min-w-0">
                                <p 
                                  className={`text-sm text-slate-700 group-hover:text-purple-700 transition-colors truncate ${
                                    item.type === 'heading' ? `ml-${Math.min(item.level * 2, 8)}` : ''
                                  }`}
                                  style={{
                                    marginLeft: item.type === 'heading' ? `${Math.min(item.level * 8, 32)}px` : '0',
                                    fontSize: item.type === 'heading' ? `${Math.max(14 - item.level, 12)}px` : '14px',
                                    fontWeight: item.type === 'heading' ? '600' : '400',
                                  }}
                                >
                                  {item.text}
                                </p>
                              </div>
                              <span className="text-xs text-slate-400 ml-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                L{item.line}
                              </span>
                            </div>
                          </motion.button>
                        ))}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default DocumentOutline;