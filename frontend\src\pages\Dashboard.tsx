import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  PlusIcon, 
  DocumentTextIcon, 
  ClockIcon, 
  ChartBarIcon,
  SparklesIcon,
  ArrowTrendingUpIcon,
  EyeIcon,
  BoltIcon,
  BeakerIcon,
  RocketLaunchIcon,
  StarIcon,
  FireIcon,
  TrophyIcon
} from '@heroicons/react/24/outline';
import { useUser } from '@/stores/authStore';
import { useAnalyticsStore, useAnalyticsSelectors } from '@/stores/analyticsStore';

const Dashboard: React.FC = () => {
  const user = useUser();
  const [mounted, setMounted] = useState(false);
  
  // Analytics hooks
  const analyticsStore = useAnalyticsStore();
  const analyticsSelectors = useAnalyticsSelectors();

  useEffect(() => {
    setMounted(true);
    // Trigger analytics calculations on dashboard load
    analyticsStore.calculateInsights();
    analyticsStore.updateStreak();
    analyticsStore.checkAchievements();
  }, []);

  // Get real analytics data
  const todayStats = analyticsSelectors.getTodayStats();
  const weekStats = analyticsSelectors.getWeekStats();
  const activeGoals = analyticsSelectors.getActiveGoals();
  const recentAchievements = analyticsSelectors.getRecentAchievements();
  
  // Calculate totals
  const totalDocuments = analyticsStore.documentAnalytics.length;
  const weeklyWords = weekStats.reduce((acc, day) => acc + day.wordsWritten, 0);
  const weeklyTime = weekStats.reduce((acc, day) => acc + day.timeSpent, 0);
  
  // Calculate trends (simplified)
  const todayWords = todayStats.wordsWritten;
  const acceptanceRate = analyticsStore.aiUsageStats.acceptanceRate;

  const stats = [
    { 
      name: 'Documents', 
      value: totalDocuments || 0, 
      icon: DocumentTextIcon,
      change: `${todayWords > 0 ? '+1 today' : 'Start writing!'}`,
      changeType: 'increase',
      gradient: 'from-violet-600 to-purple-600',
      bgGradient: 'from-violet-50 to-purple-50',
      iconBg: 'bg-violet-100'
    },
    { 
      name: 'Words This Week', 
      value: weeklyWords.toLocaleString() || '0', 
      icon: ChartBarIcon,
      change: `${todayWords > 0 ? `+${todayWords.toLocaleString()} today` : 'No words today'}`,
      changeType: todayWords > 0 ? 'increase' : 'stable',
      gradient: 'from-blue-600 to-cyan-600',
      bgGradient: 'from-blue-50 to-cyan-50',
      iconBg: 'bg-blue-100'
    },
    { 
      name: 'Writing Streak', 
      value: `${analyticsStore.writingStreak.current} days`, 
      icon: FireIcon,
      change: `Best: ${analyticsStore.writingStreak.longest} days`,
      changeType: analyticsStore.writingStreak.current > 0 ? 'increase' : 'stable',
      gradient: 'from-orange-600 to-red-600',
      bgGradient: 'from-orange-50 to-red-50',
      iconBg: 'bg-orange-100'
    },
    { 
      name: 'AI Acceptance', 
      value: `${acceptanceRate.toFixed(1)}%`, 
      icon: SparklesIcon,
      change: `${analyticsStore.aiUsageStats.acceptedSuggestions} accepted`,
      changeType: acceptanceRate > 0 ? 'increase' : 'stable',
      gradient: 'from-purple-600 to-pink-600',
      bgGradient: 'from-purple-50 to-pink-50',
      iconBg: 'bg-purple-100'
    },
  ];

  const recentDocuments = [
    {
      id: 1,
      title: 'AI Research Paper: Future of Machine Learning',
      category: 'Academic',
      words: 2547,
      updatedAt: '2 hours ago',
      preview: 'Exploring the transformative potential of artificial intelligence in reshaping modern computational paradigms...',
      progress: 75,
      categoryColor: 'bg-blue-500',
      quality: 92,
      collaborators: 3
    },
    {
      id: 2,
      title: 'Strategic Business Proposal Q1 2024',
      category: 'Professional',
      words: 1823,
      updatedAt: '1 day ago',
      preview: 'This comprehensive proposal outlines our strategic approach to market expansion and revenue optimization...',
      progress: 92,
      categoryColor: 'bg-green-500',
      quality: 89,
      collaborators: 5
    },
    {
      id: 3,
      title: 'Chapter 3: The Digital Renaissance',
      category: 'Creative',
      words: 4201,
      updatedAt: '3 days ago',
      preview: 'In an era where digital innovation meets human creativity, new frontiers of expression emerge...',
      progress: 60,
      categoryColor: 'bg-purple-500',
      quality: 95,
      collaborators: 1
    },
  ];

  const quickActions = [
    { name: 'Blog Post', icon: DocumentTextIcon, color: 'from-pink-500 to-rose-500' },
    { name: 'Research Paper', icon: BeakerIcon, color: 'from-blue-500 to-indigo-500' },
    { name: 'Creative Writing', icon: SparklesIcon, color: 'from-purple-500 to-violet-500' },
    { name: 'Business Plan', icon: RocketLaunchIcon, color: 'from-green-500 to-emerald-500' },
  ];

  return (
    <div className="mobile-full-height bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
      {/* Subtle background pattern - hidden on mobile for performance */}
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(120,119,198,0.03),transparent_50%)] hidden md:block"></div>
      
      {/* Main content */}
      <div className="relative z-10 max-w-7xl mx-auto mobile-px sm:px-8 lg:px-10 py-4 lg:py-8 lg:pl-80">
        
        {/* Hero Section */}
        <div className={`mb-6 lg:mb-12 transform transition-all duration-1000 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
          <div className="relative overflow-hidden rounded-2xl lg:rounded-3xl bg-gradient-to-r from-slate-900 via-purple-900 to-slate-900 mobile-p md:p-8 lg:p-12 shadow-2xl">
            {/* Animated background elements - hidden on mobile for performance */}
            <div className="absolute inset-0 hidden md:block">
              <div className="absolute top-0 -left-4 w-72 h-72 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
              <div className="absolute top-0 -right-4 w-72 h-72 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
            </div>
            
            <div className="relative z-10 flex items-center justify-between">
              <div className="flex-1">
                <div className="flex items-center mb-4">
                  <div className="w-3 h-3 bg-green-400 rounded-full mr-3 animate-pulse"></div>
                  <span className="text-green-400 text-sm font-semibold">All systems operational</span>
                </div>
                <h1 className="text-2xl md:text-4xl lg:text-6xl font-bold bg-gradient-to-r from-white via-purple-200 to-pink-200 bg-clip-text text-transparent mb-3 lg:mb-4 leading-tight">
                  Welcome back,<br className="hidden sm:block" />
                  <span className="sm:hidden"> </span>{user?.displayName || 'Writer'}
                </h1>
                <p className="text-base lg:text-xl text-white/80 font-medium max-w-2xl leading-relaxed">
                  Your AI-powered writing studio is ready. Let's create something extraordinary together.
                </p>
              </div>
              
              <div className="hidden lg:block">
                <div className="relative">
                  <div className="w-40 h-40 rounded-3xl bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center shadow-2xl animate-float">
                    <SparklesIcon className="w-20 h-20 text-white" />
                  </div>
                  <div className="absolute -top-2 -right-2 w-6 h-6 bg-yellow-400 rounded-full animate-ping"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className={`mb-6 lg:mb-12 transform transition-all duration-1000 delay-200 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
          <div className="flex items-center justify-between mb-4 lg:mb-8">
            <h2 className="text-xl lg:text-2xl font-bold text-slate-900">Quick Start</h2>
            <div className="flex items-center space-x-4">
              <Link
                to="/app/editor"
                className="group inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
              >
                <PlusIcon className="w-5 h-5 mr-2" />
                New Document
                <BoltIcon className="w-4 h-4 ml-2 group-hover:animate-bounce" />
              </Link>
              <button className="inline-flex items-center px-6 py-3 bg-white border border-slate-200 text-slate-700 font-semibold rounded-2xl shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200">
                <DocumentTextIcon className="w-5 h-5 mr-2" />
                Upload
              </button>
            </div>
          </div>
          
          <div className="mobile-grid-2 md:grid-cols-4 gap-3 lg:gap-4">
            {quickActions.map((action, index) => (
              <button
                key={action.name}
                className="group relative overflow-hidden bg-white rounded-xl lg:rounded-2xl mobile-p lg:p-6 shadow-md hover:shadow-xl border border-slate-100 transform hover:scale-105 transition-all duration-300"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className={`w-10 h-10 lg:w-12 lg:h-12 rounded-lg lg:rounded-xl bg-gradient-to-r ${action.color} flex items-center justify-center mb-3 lg:mb-4 group-hover:scale-110 transition-transform duration-200`}>
                  <action.icon className="w-5 h-5 lg:w-6 lg:h-6 text-white" />
                </div>
                <h3 className="font-semibold text-slate-900 text-xs lg:text-sm">{action.name}</h3>
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
              </button>
            ))}
          </div>
        </div>

        {/* Stats Section */}
        <div className={`mb-6 lg:mb-12 transform transition-all duration-1000 delay-400 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
          <div className="flex items-center justify-between mb-4 lg:mb-8">
            <h2 className="text-xl lg:text-2xl font-bold text-slate-900">Analytics</h2>
            <Link to="/app/analytics" className="text-purple-600 hover:text-purple-700 font-medium flex items-center text-sm lg:text-base">
              <span className="hidden sm:inline">View detailed analytics</span>
              <span className="sm:hidden">Details</span>
              <ArrowTrendingUpIcon className="w-4 h-4 ml-1" />
            </Link>
          </div>
          
          <div className="mobile-grid-1 md:grid-cols-3 gap-4 lg:gap-6">
            {stats.map((stat, index) => {
              const Icon = stat.icon;
              return (
                <div
                  key={stat.name}
                  className={`relative overflow-hidden bg-gradient-to-br ${stat.bgGradient} rounded-2xl lg:rounded-3xl mobile-p lg:p-8 border border-white/50 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-500 group cursor-pointer`}
                  style={{ animationDelay: `${index * 150}ms` }}
                >
                  <div className="flex items-start justify-between mb-4 lg:mb-6">
                    <div className={`p-3 lg:p-4 rounded-xl lg:rounded-2xl ${stat.iconBg} shadow-sm group-hover:shadow-md transition-shadow duration-200`}>
                      <Icon className="h-6 w-6 lg:h-8 lg:w-8 text-slate-700" />
                    </div>
                    <div className="text-right">
                      <div className="text-xs lg:text-sm font-semibold text-green-600 flex items-center">
                        <ArrowTrendingUpIcon className="w-3 h-3 lg:w-4 lg:h-4 mr-1" />
                        <span className="hidden sm:inline">{stat.change}</span>
                        <span className="sm:hidden">+{stat.changeType === 'increase' ? '↑' : '→'}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <dt className="text-sm lg:text-lg font-semibold text-slate-600 mb-1 lg:mb-2">
                      {stat.name}
                    </dt>
                    <dd className="text-2xl lg:text-4xl font-bold text-slate-900 group-hover:scale-110 transition-transform duration-300">
                      {stat.value}
                    </dd>
                  </div>
                  
                  {/* Subtle hover effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Recent Documents */}
        <div className={`transform transition-all duration-1000 delay-600 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
          <div className="flex items-center justify-between mb-4 lg:mb-8">
            <h2 className="text-xl lg:text-2xl font-bold text-slate-900">Recent Work</h2>
            <Link
              to="/app/editor"
              className="text-purple-600 hover:text-purple-700 font-medium flex items-center group text-sm lg:text-base"
            >
              <span className="hidden sm:inline">Create new document</span>
              <span className="sm:hidden">New</span>
              <ArrowTrendingUpIcon className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
            </Link>
          </div>
          
          <div className="space-y-3 lg:space-y-6">
            {recentDocuments.map((document, index) => (
              <div
                key={document.id}
                className="group relative bg-white rounded-2xl lg:rounded-3xl border border-slate-100 shadow-lg hover:shadow-xl transition-all duration-500 transform hover:scale-[1.02] overflow-hidden"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <Link to={`/app/editor/${document.id}`} className="block mobile-p lg:p-8">
                  <div className="flex items-start justify-between mb-4 lg:mb-6">
                    <div className="flex items-center space-x-2 lg:space-x-4 flex-wrap">
                      <div className={`w-3 h-3 lg:w-4 lg:h-4 ${document.categoryColor} rounded-full shadow-sm`}></div>
                      <span className="text-xs lg:text-sm font-semibold text-slate-500 uppercase tracking-wider">
                        {document.category}
                      </span>
                      <div className="hidden sm:flex items-center space-x-2 text-slate-400">
                        <EyeIcon className="w-4 h-4" />
                        <span className="text-sm">{Math.floor(Math.random() * 100) + 50}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <StarIcon className="w-3 h-3 lg:w-4 lg:h-4 text-yellow-400 fill-current" />
                        <span className="text-xs lg:text-sm font-medium text-slate-600">{document.quality}%</span>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-xs lg:text-sm text-slate-500 mb-1">{document.updatedAt}</div>
                      <div className="hidden lg:flex items-center space-x-2">
                        <div className="flex -space-x-1">
                          {Array.from({ length: document.collaborators }).map((_, i) => (
                            <div key={i} className="w-6 h-6 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full border-2 border-white"></div>
                          ))}
                        </div>
                        <span className="text-xs text-slate-400">+{document.collaborators}</span>
                      </div>
                    </div>
                  </div>
                  
                  <h3 className="text-lg lg:text-2xl font-bold text-slate-900 mb-3 lg:mb-4 group-hover:text-purple-700 transition-colors">
                    {document.title}
                  </h3>
                  
                  <p className="text-slate-600 text-sm lg:text-lg mb-4 lg:mb-6 leading-relaxed line-clamp-2">
                    {document.preview}
                  </p>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-6">
                      <div className="text-slate-600">
                        <span className="font-semibold">{document.words.toLocaleString()}</span> words
                      </div>
                      <div className="flex items-center space-x-3">
                        <div className="w-32 bg-slate-200 rounded-full h-2 overflow-hidden">
                          <div 
                            className={`h-full bg-gradient-to-r ${document.categoryColor.replace('bg-', 'from-')} to-transparent transition-all duration-1000`}
                            style={{ width: `${document.progress}%` }}
                          ></div>
                        </div>
                        <span className="text-sm text-slate-500 font-medium">{document.progress}% complete</span>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                      <button className="p-2 rounded-lg bg-slate-100 hover:bg-slate-200 transition-colors">
                        <DocumentTextIcon className="w-4 h-4 text-slate-600" />
                      </button>
                      <button className="p-2 rounded-lg bg-slate-100 hover:bg-slate-200 transition-colors">
                        <StarIcon className="w-4 h-4 text-slate-600" />
                      </button>
                    </div>
                  </div>
                </Link>
                
                {/* Subtle shine effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
              </div>
            ))}
          </div>
        </div>

        {/* Goals and Achievements Section */}
        {(activeGoals.length > 0 || recentAchievements.length > 0) && (
          <div className={`mb-12 transform transition-all duration-1000 delay-800 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              
              {/* Active Goals */}
              {activeGoals.length > 0 && (
                <div className="bg-white rounded-3xl border border-slate-100 shadow-lg p-8">
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-xl font-bold text-slate-900 flex items-center">
                      <TrophyIcon className="w-6 h-6 mr-2 text-yellow-500" />
                      Active Goals
                    </h2>
                    <Link
                      to="/app/analytics"
                      className="text-purple-600 hover:text-purple-700 font-medium flex items-center group"
                    >
                      View all
                      <ArrowTrendingUpIcon className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
                    </Link>
                  </div>
                  
                  <div className="space-y-4">
                    {activeGoals.slice(0, 3).map((goal) => {
                      const progress = Math.min((goal.current / goal.target) * 100, 100);
                      return (
                        <div key={goal.id} className="p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl border border-purple-100">
                          <div className="flex items-center justify-between mb-2">
                            <h3 className="font-semibold text-slate-900 capitalize">
                              {goal.type.replace('_', ' ')}
                            </h3>
                            <span className="text-sm text-slate-600">
                              {goal.current.toLocaleString()}/{goal.target.toLocaleString()}
                            </span>
                          </div>
                          <div className="w-full bg-white/60 rounded-full h-3 mb-2 overflow-hidden">
                            <div
                              className="h-full bg-gradient-to-r from-purple-500 to-pink-500 transition-all duration-500"
                              style={{ width: `${progress}%` }}
                            />
                          </div>
                          <p className="text-xs text-purple-700 font-medium">
                            {progress.toFixed(1)}% complete
                          </p>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}

              {/* Recent Achievements */}
              {recentAchievements.length > 0 && (
                <div className="bg-white rounded-3xl border border-slate-100 shadow-lg p-8">
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-xl font-bold text-slate-900 flex items-center">
                      <StarIcon className="w-6 h-6 mr-2 text-yellow-500" />
                      Recent Achievements
                    </h2>
                    <Link
                      to="/app/analytics"
                      className="text-purple-600 hover:text-purple-700 font-medium flex items-center group"
                    >
                      View all
                      <ArrowTrendingUpIcon className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
                    </Link>
                  </div>
                  
                  <div className="space-y-4">
                    {recentAchievements.slice(0, 3).map((achievement) => {
                      const rarityColors = {
                        common: 'from-slate-50 to-slate-100 border-slate-200',
                        rare: 'from-blue-50 to-blue-100 border-blue-200',
                        epic: 'from-purple-50 to-purple-100 border-purple-200',
                        legendary: 'from-yellow-50 to-yellow-100 border-yellow-200',
                      };
                      
                      return (
                        <div
                          key={achievement.id}
                          className={`p-4 rounded-2xl border bg-gradient-to-r ${rarityColors[achievement.rarity]} hover:shadow-md transition-shadow`}
                        >
                          <div className="flex items-center">
                            <span className="text-2xl mr-3">{achievement.icon}</span>
                            <div className="flex-1">
                              <h3 className="font-semibold text-slate-900">{achievement.title}</h3>
                              <p className="text-sm text-slate-600">{achievement.description}</p>
                              {achievement.unlockedAt && (
                                <p className="text-xs text-slate-500 mt-1">
                                  Unlocked {achievement.unlockedAt.toLocaleDateString()}
                                </p>
                              )}
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Dashboard;