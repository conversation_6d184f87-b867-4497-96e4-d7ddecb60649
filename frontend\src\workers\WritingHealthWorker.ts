// Mock Writing Health Worker for frontend-only implementation
// In a full implementation, this would use libraries like:
// - text-statistics for reading level
// - retext-passive for passive voice detection
// - alex for inclusive language checking

interface WritingHealthScore {
  overall: number;
  readingLevel: number;
  wordiness: number;
  passiveVoice: number;
  inclusiveLanguage: number;
  brandVoice: number;
}

interface HealthIssue {
  id: string;
  type: 'readingLevel' | 'wordiness' | 'passiveVoice' | 'inclusiveLanguage' | 'brandVoice';
  severity: 'low' | 'medium' | 'high';
  sentence: string;
  suggestion: string;
  line: number;
  startOffset: number;
  endOffset: number;
}

interface AnalysisResult {
  score: WritingHealthScore;
  issues: HealthIssue[];
  wordCount: number;
  sentenceCount: number;
  paragraphCount: number;
}

class WritingHealthAnalyzer {
  private passiveVoicePatterns = [
    /\b(was|were|is|am|are|be|been|being)\s+\w*ed\b/gi,
    /\b(was|were|is|am|are|be|been|being)\s+\w*en\b/gi,
  ];

  private wordinessPatterns = [
    /\b(due to the fact that|in order to|for the purpose of|in spite of the fact that)\b/gi,
    /\b(it is important to note that|it should be noted that|it is worth mentioning)\b/gi,
    /\b(a large number of|a great deal of|the majority of)\b/gi,
  ];

  private inclusiveLanguageFlags = [
    { pattern: /\b(guys|mankind|manpower)\b/gi, suggestion: 'Consider using inclusive alternatives' },
    { pattern: /\b(crazy|insane|mad)\b/gi, suggestion: 'Consider using less stigmatizing language' },
    { pattern: /\b(lame|dumb|blind to)\b/gi, suggestion: 'Consider more inclusive language' },
  ];

  analyzeText(text: string): AnalysisResult {
    const sentences = this.extractSentences(text);
    const words = this.extractWords(text);
    const paragraphs = this.extractParagraphs(text);

    // Calculate individual scores
    const readingLevel = this.calculateReadingLevel(text, sentences, words);
    const wordiness = this.calculateWordiness(sentences);
    const passiveVoice = this.calculatePassiveVoice(text, sentences);
    const inclusiveLanguage = this.calculateInclusiveLanguage(text);
    const brandVoice = this.calculateBrandVoice(text);

    // Calculate overall score
    const overall = Math.round(
      (readingLevel + wordiness + passiveVoice + inclusiveLanguage + brandVoice) / 5
    );

    const score: WritingHealthScore = {
      overall,
      readingLevel,
      wordiness,
      passiveVoice,
      inclusiveLanguage,
      brandVoice,
    };

    // Generate issues
    const issues = this.generateIssues(text, sentences);

    return {
      score,
      issues,
      wordCount: words.length,
      sentenceCount: sentences.length,
      paragraphCount: paragraphs.length,
    };
  }

  private extractSentences(text: string): string[] {
    return text
      .split(/[.!?]+/)
      .map(s => s.trim())
      .filter(s => s.length > 0);
  }

  private extractWords(text: string): string[] {
    return text
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(w => w.length > 0);
  }

  private extractParagraphs(text: string): string[] {
    return text
      .split(/\n\s*\n/)
      .map(p => p.trim())
      .filter(p => p.length > 0);
  }

  private calculateReadingLevel(text: string, sentences: string[], words: string[]): number {
    if (sentences.length === 0 || words.length === 0) return 100;

    // Simplified Flesch Reading Ease calculation
    const avgSentenceLength = words.length / sentences.length;
    const avgSyllables = this.estimateAverageSyllables(words);

    // Flesch Reading Ease = 206.835 - (1.015 × ASL) - (84.6 × ASW)
    const fleschScore = 206.835 - (1.015 * avgSentenceLength) - (84.6 * avgSyllables);
    
    // Convert to 0-100 scale where higher is better
    return Math.max(0, Math.min(100, Math.round(fleschScore)));
  }

  private estimateAverageSyllables(words: string[]): number {
    const syllableCount = words.reduce((total, word) => {
      return total + this.countSyllables(word);
    }, 0);
    return syllableCount / words.length;
  }

  private countSyllables(word: string): number {
    // Simple syllable counting heuristic
    word = word.toLowerCase();
    if (word.length <= 3) return 1;
    
    let count = 0;
    const vowels = 'aeiouy';
    let previousWasVowel = false;

    for (let i = 0; i < word.length; i++) {
      const isVowel = vowels.includes(word[i]);
      if (isVowel && !previousWasVowel) {
        count++;
      }
      previousWasVowel = isVowel;
    }

    // Handle silent e
    if (word.endsWith('e')) {
      count--;
    }

    return Math.max(1, count);
  }

  private calculateWordiness(sentences: string[]): number {
    if (sentences.length === 0) return 100;

    const avgSentenceLength = sentences.reduce((total, sentence) => {
      const words = sentence.split(/\s+/).filter(w => w.length > 0);
      return total + words.length;
    }, 0) / sentences.length;

    // Target: 15-20 words per sentence
    const targetLength = 17.5;
    const deviation = Math.abs(avgSentenceLength - targetLength);
    const score = Math.max(0, 100 - (deviation * 3));

    return Math.round(score);
  }

  private calculatePassiveVoice(text: string, sentences: string[]): number {
    if (sentences.length === 0) return 100;

    let passiveCount = 0;
    sentences.forEach(sentence => {
      this.passiveVoicePatterns.forEach(pattern => {
        if (pattern.test(sentence)) {
          passiveCount++;
        }
      });
    });

    const passivePercentage = (passiveCount / sentences.length) * 100;
    const score = Math.max(0, 100 - passivePercentage * 2);

    return Math.round(score);
  }

  private calculateInclusiveLanguage(text: string): number {
    let flaggedCount = 0;
    const words = this.extractWords(text);

    this.inclusiveLanguageFlags.forEach(flag => {
      const matches = text.match(flag.pattern);
      if (matches) {
        flaggedCount += matches.length;
      }
    });

    const flaggedPercentage = (flaggedCount / words.length) * 100;
    const score = Math.max(0, 100 - flaggedPercentage * 10);

    return Math.round(score);
  }

  private calculateBrandVoice(text: string): number {
    // Mock brand voice calculation
    // In reality, this would compare against a brand voice model
    const words = this.extractWords(text);
    
    // Mock: penalize very short or very long texts
    if (words.length < 50) return 70;
    if (words.length > 5000) return 75;

    // Mock: reward balanced sentence structure
    const sentences = this.extractSentences(text);
    const avgSentenceLength = words.length / Math.max(1, sentences.length);
    
    let score = 85;
    if (avgSentenceLength < 10 || avgSentenceLength > 25) {
      score -= 10;
    }

    return Math.round(score);
  }

  private generateIssues(text: string, sentences: string[]): HealthIssue[] {
    const issues: HealthIssue[] = [];
    let currentOffset = 0;

    sentences.forEach((sentence, index) => {
      const lineNumber = index + 1;

      // Check for passive voice
      this.passiveVoicePatterns.forEach(pattern => {
        const matches = sentence.match(pattern);
        if (matches) {
          issues.push({
            id: `passive-${index}-${Date.now()}`,
            type: 'passiveVoice',
            severity: 'medium',
            sentence: sentence.trim(),
            suggestion: 'Consider rewriting in active voice for more direct communication.',
            line: lineNumber,
            startOffset: currentOffset,
            endOffset: currentOffset + sentence.length,
          });
        }
      });

      // Check for wordiness
      this.wordinessPatterns.forEach(pattern => {
        const matches = sentence.match(pattern);
        if (matches) {
          issues.push({
            id: `wordiness-${index}-${Date.now()}`,
            type: 'wordiness',
            severity: 'low',
            sentence: sentence.trim(),
            suggestion: 'Consider using simpler, more direct language.',
            line: lineNumber,
            startOffset: currentOffset,
            endOffset: currentOffset + sentence.length,
          });
        }
      });

      // Check for inclusive language
      this.inclusiveLanguageFlags.forEach(flag => {
        const matches = sentence.match(flag.pattern);
        if (matches) {
          issues.push({
            id: `inclusive-${index}-${Date.now()}`,
            type: 'inclusiveLanguage',
            severity: 'high',
            sentence: sentence.trim(),
            suggestion: flag.suggestion,
            line: lineNumber,
            startOffset: currentOffset,
            endOffset: currentOffset + sentence.length,
          });
        }
      });

      // Check reading level (long sentences)
      const words = sentence.split(/\s+/).filter(w => w.length > 0);
      if (words.length > 30) {
        issues.push({
          id: `reading-${index}-${Date.now()}`,
          type: 'readingLevel',
          severity: 'medium',
          sentence: sentence.trim(),
          suggestion: 'Consider breaking this long sentence into shorter ones for better readability.',
          line: lineNumber,
          startOffset: currentOffset,
          endOffset: currentOffset + sentence.length,
        });
      }

      currentOffset += sentence.length + 1; // +1 for sentence delimiter
    });

    return issues;
  }
}

// Worker message handling
const analyzer = new WritingHealthAnalyzer();

self.onmessage = (event) => {
  const { text, requestId } = event.data;
  
  try {
    const result = analyzer.analyzeText(text);
    self.postMessage({
      requestId,
      success: true,
      result,
    });
  } catch (error) {
    self.postMessage({
      requestId,
      success: false,
      error: error instanceof Error ? error.message : 'Analysis failed',
    });
  }
};

// Export for use in non-worker contexts
export { WritingHealthAnalyzer, type WritingHealthScore, type HealthIssue, type AnalysisResult };