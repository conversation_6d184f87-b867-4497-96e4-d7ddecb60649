"""
Writing Health Analysis API Endpoints

Purpose: Provides REST API endpoints for writing health analysis including:
- Real-time text health analysis and scoring
- Historical health metrics and trends
- Health issue detection and resolution tracking
- Personalized health insights and recommendations

Responsibilities:
- Text health analysis request handling
- Health metrics calculation and storage
- Health trends and analytics generation
- Issue categorization and resolution guidance
- User health preferences management

Used by: Frontend health dashboard, real-time health workers, analytics components
Dependencies: core.multi_agent_engine, health analysis workers, core.auth
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import structlog

from api.auth import get_current_user
from core.auth import UserClaims
from mock_data import get_mock_health_metrics

# Configure logging
logger = structlog.get_logger(__name__)

# Create router
router = APIRouter()

# Request/Response Models
class HealthAnalysisRequest(BaseModel):
    """Request model for text health analysis."""
    text: str = Field(..., min_length=1, description="Text to analyze")
    block_id: Optional[str] = Field(None, description="Associated block ID")
    document_id: Optional[str] = Field(None, description="Associated document ID")
    analysis_type: str = Field(default="comprehensive", description="Type of analysis to perform")
    preferences: Optional[Dict[str, Any]] = Field(default_factory=dict)

class BatchHealthAnalysisRequest(BaseModel):
    """Request model for batch health analysis."""
    texts: List[str] = Field(..., min_items=1, max_items=50)
    block_ids: Optional[List[str]] = None
    document_id: Optional[str] = None
    analysis_type: str = Field(default="comprehensive")

class HealthIssueResolution(BaseModel):
    """Request model for resolving a health issue."""
    issue_id: str
    resolution_type: str = Field(..., description="How the issue was resolved")
    user_feedback: Optional[str] = Field(None, max_length=500)

class HealthPreferencesUpdate(BaseModel):
    """Request model for updating health preferences."""
    focus_areas: Optional[List[str]] = None
    scoring_weights: Optional[Dict[str, float]] = None
    notification_threshold: Optional[int] = Field(None, ge=0, le=100)
    auto_analysis: Optional[bool] = None

class HealthResponse(BaseModel):
    """Response model for health analysis."""
    success: bool
    data: Dict[str, Any]

class HealthListResponse(BaseModel):
    """Response model for health data lists."""
    success: bool
    data: List[Dict[str, Any]]
    meta: Dict[str, Any]

@router.post("/analyze", response_model=HealthResponse)
async def analyze_text_health(
    analysis_request: HealthAnalysisRequest,
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Analyze text health metrics and provide improvement suggestions.
    
    Args:
        analysis_request: Text analysis request data
        current_user: Current authenticated user
        
    Returns:
        HealthResponse: Health analysis results
    """
    try:
        logger.info(
            "Analyzing text health",
            user_id=current_user.user_id,
            text_length=len(analysis_request.text),
            analysis_type=analysis_request.analysis_type,
            block_id=analysis_request.block_id
        )
        
        # Get mock health analysis
        health_analysis = get_mock_health_metrics()
        
        # Customize analysis based on text content
        text_words = len(analysis_request.text.split())
        text_chars = len(analysis_request.text)
        
        # Adjust scores based on text length (simple heuristic)
        if text_words < 10:
            health_analysis["metrics"]["readability"]["score"] = max(health_analysis["metrics"]["readability"]["score"] - 10, 0)
        elif text_words > 100:
            health_analysis["metrics"]["readability"]["score"] = min(health_analysis["metrics"]["readability"]["score"] + 5, 100)
        
        # Add request-specific metadata
        health_analysis.update({
            "block_id": analysis_request.block_id,
            "document_id": analysis_request.document_id,
            "analysis_type": analysis_request.analysis_type,
            "text_stats": {
                "word_count": text_words,
                "character_count": text_chars,
                "sentence_count": analysis_request.text.count('.') + analysis_request.text.count('!') + analysis_request.text.count('?'),
                "paragraph_count": analysis_request.text.count('\n\n') + 1
            }
        })
        
        logger.info(
            "Text health analysis completed",
            overall_score=health_analysis["overall_score"],
            issues_found=len(health_analysis["issues"])
        )
        
        return HealthResponse(
            success=True,
            data=health_analysis
        )
        
    except Exception as e:
        logger.error("Failed to analyze text health", error=str(e), user_id=current_user.user_id)
        raise HTTPException(
            status_code=500,
            detail="Failed to analyze text health"
        )

@router.post("/batch-analyze", response_model=HealthResponse)
async def batch_analyze_health(
    batch_request: BatchHealthAnalysisRequest,
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Perform batch health analysis on multiple texts.
    
    Args:
        batch_request: Batch analysis request data
        current_user: Current authenticated user
        
    Returns:
        HealthResponse: Batch analysis results
    """
    try:
        logger.info(
            "Performing batch health analysis",
            user_id=current_user.user_id,
            text_count=len(batch_request.texts),
            document_id=batch_request.document_id
        )
        
        # Process each text
        results = []
        total_score = 0
        total_issues = 0
        
        for i, text in enumerate(batch_request.texts):
            health_analysis = get_mock_health_metrics()
            
            # Customize for each text
            text_words = len(text.split())
            block_id = batch_request.block_ids[i] if batch_request.block_ids and i < len(batch_request.block_ids) else None
            
            health_analysis.update({
                "block_id": block_id,
                "document_id": batch_request.document_id,
                "text_index": i,
                "text_stats": {
                    "word_count": text_words,
                    "character_count": len(text)
                }
            })
            
            results.append(health_analysis)
            total_score += health_analysis["overall_score"]
            total_issues += len(health_analysis["issues"])
        
        # Calculate batch summary
        batch_summary = {
            "batch_id": f"batch_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
            "document_id": batch_request.document_id,
            "analysis_timestamp": datetime.utcnow().isoformat() + "Z",
            "texts_analyzed": len(batch_request.texts),
            "average_score": round(total_score / len(batch_request.texts), 1),
            "total_issues": total_issues,
            "results": results,
            "execution_time": 2.4
        }
        
        logger.info(
            "Batch health analysis completed",
            texts_analyzed=len(batch_request.texts),
            average_score=batch_summary["average_score"]
        )
        
        return HealthResponse(
            success=True,
            data=batch_summary
        )
        
    except Exception as e:
        logger.error("Failed to perform batch health analysis", error=str(e), user_id=current_user.user_id)
        raise HTTPException(
            status_code=500,
            detail="Failed to perform batch health analysis"
        )

@router.get("/trends", response_model=HealthResponse)
async def get_health_trends(
    current_user: UserClaims = Depends(get_current_user),
    days: int = Query(default=30, ge=1, le=365),
    document_id: Optional[str] = Query(default=None),
    metric_type: Optional[str] = Query(default=None)
):
    """
    Get health trends and analytics for the user.
    
    Args:
        current_user: Current authenticated user
        days: Number of days to include in trends
        document_id: Filter by specific document
        metric_type: Filter by specific metric type
        
    Returns:
        HealthResponse: Health trends data
    """
    try:
        logger.info(
            "Getting health trends",
            user_id=current_user.user_id,
            days=days,
            document_id=document_id,
            metric_type=metric_type
        )
        
        # Generate mock trend data
        trend_data = []
        base_date = datetime.utcnow() - timedelta(days=days)
        
        for i in range(days):
            date = base_date + timedelta(days=i)
            
            # Simulate gradual improvement over time
            base_score = 75 + (i / days) * 15  # Improve from 75 to 90
            daily_variation = (i % 7) * 2 - 6  # Weekly pattern
            
            trend_data.append({
                "date": date.strftime("%Y-%m-%d"),
                "overall_score": min(max(base_score + daily_variation, 0), 100),
                "readability_score": min(max(base_score + daily_variation - 5, 0), 100),
                "clarity_score": min(max(base_score + daily_variation + 3, 0), 100),
                "voice_score": min(max(base_score + daily_variation + 1, 0), 100),
                "inclusivity_score": min(max(base_score + daily_variation + 8, 0), 100),
                "brand_score": min(max(base_score + daily_variation - 2, 0), 100),
                "analyses_count": max(1, (i % 5) + 1),
                "issues_resolved": i % 3
            })
        
        # Calculate summary statistics
        recent_scores = [d["overall_score"] for d in trend_data[-7:]]  # Last 7 days
        older_scores = [d["overall_score"] for d in trend_data[-14:-7]]  # Previous 7 days
        
        trends_summary = {
            "user_id": current_user.user_id,
            "time_period": f"{days}_days",
            "document_id": document_id,
            "metric_type": metric_type,
            "generated_at": datetime.utcnow().isoformat() + "Z",
            "summary": {
                "current_average": round(sum(recent_scores) / len(recent_scores), 1),
                "previous_average": round(sum(older_scores) / len(older_scores), 1) if older_scores else 0,
                "improvement": round(sum(recent_scores) / len(recent_scores) - (sum(older_scores) / len(older_scores) if older_scores else 75), 1),
                "total_analyses": sum(d["analyses_count"] for d in trend_data),
                "total_issues_resolved": sum(d["issues_resolved"] for d in trend_data),
                "best_day": max(trend_data, key=lambda x: x["overall_score"])["date"],
                "worst_day": min(trend_data, key=lambda x: x["overall_score"])["date"]
            },
            "daily_data": trend_data,
            "insights": [
                "Your writing health has improved by 12% over the past month",
                "Clarity scores show the most consistent improvement",
                "Tuesday and Wednesday tend to be your most productive writing days",
                "Consider focusing on readability improvements for even better scores"
            ]
        }
        
        logger.info(
            "Health trends retrieved",
            current_average=trends_summary["summary"]["current_average"],
            improvement=trends_summary["summary"]["improvement"]
        )
        
        return HealthResponse(
            success=True,
            data=trends_summary
        )
        
    except Exception as e:
        logger.error("Failed to get health trends", error=str(e), user_id=current_user.user_id)
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve health trends"
        )

@router.get("/issues/{block_id}", response_model=HealthListResponse)
async def get_block_health_issues(
    block_id: str,
    current_user: UserClaims = Depends(get_current_user),
    severity: Optional[str] = Query(default=None),
    status: Optional[str] = Query(default=None)
):
    """
    Get health issues for a specific block.
    
    Args:
        block_id: Block ID to get issues for
        current_user: Current authenticated user
        severity: Filter by issue severity
        status: Filter by issue status
        
    Returns:
        HealthListResponse: List of health issues
    """
    try:
        logger.info(
            "Getting block health issues",
            block_id=block_id,
            user_id=current_user.user_id,
            severity=severity,
            status=status
        )
        
        # Mock health issues
        issues = [
            {
                "id": "issue_001",
                "block_id": block_id,
                "type": "readability",
                "severity": "medium",
                "status": "active",
                "position": {"start": 45, "end": 89},
                "description": "Sentence too long (32 words)",
                "suggestion": "Consider breaking into two sentences",
                "impact": "Reduces reading comprehension",
                "confidence": 0.89,
                "detected_at": "2024-06-25T14:30:00Z",
                "category": "sentence_length"
            },
            {
                "id": "issue_002",
                "block_id": block_id,
                "type": "clarity",
                "severity": "low",
                "status": "active",
                "position": {"start": 150, "end": 165},
                "description": "Vague pronoun reference",
                "suggestion": "Replace 'it' with specific noun",
                "impact": "May confuse readers",
                "confidence": 0.72,
                "detected_at": "2024-06-25T14:30:00Z",
                "category": "pronoun_clarity"
            },
            {
                "id": "issue_003",
                "block_id": block_id,
                "type": "voice_consistency",
                "severity": "high",
                "status": "resolved",
                "position": {"start": 200, "end": 220},
                "description": "Inconsistent tense usage",
                "suggestion": "Maintain past tense throughout",
                "impact": "Disrupts narrative flow",
                "confidence": 0.95,
                "detected_at": "2024-06-24T10:15:00Z",
                "resolved_at": "2024-06-24T16:30:00Z",
                "category": "tense_consistency"
            }
        ]
        
        # Apply filters
        filtered_issues = issues
        if severity:
            filtered_issues = [i for i in filtered_issues if i["severity"] == severity]
        if status:
            filtered_issues = [i for i in filtered_issues if i["status"] == status]
        
        return HealthListResponse(
            success=True,
            data=filtered_issues,
            meta={
                "total": len(filtered_issues),
                "block_id": block_id,
                "active_issues": len([i for i in filtered_issues if i["status"] == "active"]),
                "resolved_issues": len([i for i in filtered_issues if i["status"] == "resolved"])
            }
        )
        
    except Exception as e:
        logger.error("Failed to get block health issues", error=str(e), block_id=block_id)
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve block health issues"
        )

@router.post("/resolve-issue", response_model=HealthResponse)
async def resolve_health_issue(
    resolution: HealthIssueResolution,
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Mark a health issue as resolved.
    
    Args:
        resolution: Issue resolution data
        current_user: Current authenticated user
        
    Returns:
        HealthResponse: Resolution confirmation
    """
    try:
        logger.info(
            "Resolving health issue",
            issue_id=resolution.issue_id,
            user_id=current_user.user_id,
            resolution_type=resolution.resolution_type
        )
        
        # Mock issue resolution
        resolved_issue = {
            "issue_id": resolution.issue_id,
            "status": "resolved",
            "resolution_type": resolution.resolution_type,
            "user_feedback": resolution.user_feedback,
            "resolved_at": datetime.utcnow().isoformat() + "Z",
            "resolved_by": current_user.user_id
        }
        
        logger.info("Health issue resolved successfully", issue_id=resolution.issue_id)
        
        return HealthResponse(
            success=True,
            data={
                "message": "Health issue resolved successfully",
                "resolution": resolved_issue
            }
        )
        
    except Exception as e:
        logger.error("Failed to resolve health issue", error=str(e), issue_id=resolution.issue_id)
        raise HTTPException(
            status_code=500,
            detail="Failed to resolve health issue"
        )

@router.get("/preferences", response_model=HealthResponse)
async def get_health_preferences(
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Get user's health analysis preferences.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        HealthResponse: User health preferences
    """
    try:
        logger.info("Getting health preferences", user_id=current_user.user_id)
        
        # Mock user preferences
        preferences = {
            "user_id": current_user.user_id,
            "focus_areas": ["readability", "clarity", "voice_consistency"],
            "scoring_weights": {
                "readability": 0.25,
                "clarity": 0.25,
                "voice_consistency": 0.20,
                "inclusivity": 0.15,
                "brand_alignment": 0.15
            },
            "notification_threshold": 75,
            "auto_analysis": True,
            "analysis_frequency": "on_change",
            "detailed_explanations": True,
            "show_confidence_scores": True,
            "updated_at": "2024-06-20T10:30:00Z"
        }
        
        return HealthResponse(
            success=True,
            data=preferences
        )
        
    except Exception as e:
        logger.error("Failed to get health preferences", error=str(e), user_id=current_user.user_id)
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve health preferences"
        )

@router.put("/preferences", response_model=HealthResponse)
async def update_health_preferences(
    preferences: HealthPreferencesUpdate,
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Update user's health analysis preferences.
    
    Args:
        preferences: Updated preference data
        current_user: Current authenticated user
        
    Returns:
        HealthResponse: Updated preferences
    """
    try:
        logger.info("Updating health preferences", user_id=current_user.user_id)
        
        # Mock preference update
        updated_preferences = {
            "user_id": current_user.user_id,
            "focus_areas": preferences.focus_areas or ["readability", "clarity", "voice_consistency"],
            "scoring_weights": preferences.scoring_weights or {
                "readability": 0.25,
                "clarity": 0.25,
                "voice_consistency": 0.20,
                "inclusivity": 0.15,
                "brand_alignment": 0.15
            },
            "notification_threshold": preferences.notification_threshold or 75,
            "auto_analysis": preferences.auto_analysis if preferences.auto_analysis is not None else True,
            "updated_at": datetime.utcnow().isoformat() + "Z"
        }
        
        logger.info("Health preferences updated successfully", user_id=current_user.user_id)
        
        return HealthResponse(
            success=True,
            data={
                "message": "Health preferences updated successfully",
                "preferences": updated_preferences
            }
        )
        
    except Exception as e:
        logger.error("Failed to update health preferences", error=str(e), user_id=current_user.user_id)
        raise HTTPException(
            status_code=500,
            detail="Failed to update health preferences"
        )