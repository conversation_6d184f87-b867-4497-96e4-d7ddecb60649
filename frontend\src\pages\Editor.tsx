import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { SparklesIcon, ChevronLeftIcon, CloudArrowUpIcon, BookOpenIcon, ClockIcon, UserGroupIcon } from '@heroicons/react/24/outline';
import { shouldUseMobileEditor } from '../utils/deviceDetection';
import MobileEditor from '../components/editor/MobileEditor';
import EditorHeader from '../components/editor/EditorHeader';
import EditorToolbar from '../components/editor/EditorToolbar';
import EnhancedAIPanel from '../components/editor/EnhancedAIPanel';
import CommandPalette from '../components/editor/CommandPalette';
import StatusBar from '../components/editor/StatusBar';
import LeftToolbar from '../components/editor/LeftToolbar';
import WritingHealthBadge from '../components/editor/WritingHealthBadge';
import WritingHealthPanel from '../components/editor/WritingHealthPanel';
import SmartCitationSearch from '../components/editor/SmartCitationSearch';
import CitationManager from '../components/editor/CitationManager';
import VersionControl from '../components/editor/VersionControl';
import ImportWizard from '../components/editor/ImportWizard';
import LivePresence from '../components/editor/LivePresence';
import { WritingHealthAnalyzer, WritingHealthScore, HealthIssue } from '../workers/WritingHealthWorker';
import { useAnalyticsService } from '../services/analytics';
import { useAnalyticsStore } from '../stores/analyticsStore';
import DocumentAnalyticsPanel from '../components/editor/DocumentAnalyticsPanel';
import PersonaFeedbackPanel from '../components/persona/PersonaFeedbackPanel';
import { usePersonaStore } from '../stores/personaStore';

interface Document {
  id: string;
  title: string;
  content: string;
  lastSaved: Date | null;
  collaborators: Array<{
    id: string;
    name: string;
    avatar?: string;
    isOnline: boolean;
  }>;
}

interface AISuggestion {
  id: string;
  type: 'grammar' | 'style' | 'content' | 'structure';
  severity: 'low' | 'medium' | 'high';
  title: string;
  description: string;
  originalText: string;
  suggestedText: string;
  line: number;
  isStale?: boolean;
}

interface Citation {
  id: string;
  title: string;
  authors: string[];
  year: number;
  source: string;
  type: 'journal' | 'book' | 'website' | 'report' | 'article';
  doi?: string;
  url?: string;
  abstract?: string;
  tags?: string[];
  style: 'apa' | 'mla' | 'chicago';
  usageCount: number;
  dateAdded: Date;
}

interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  color: string;
  isOnline: boolean;
  lastSeen?: Date;
}

interface UserPresence extends User {
  status: 'active' | 'idle' | 'away' | 'offline';
  cursor?: {
    x: number;
    y: number;
    selection?: { start: number; end: number };
  };
  currentAction?: 'typing' | 'selecting' | 'editing' | 'viewing';
  location?: { line: number; character: number };
}

interface Checkpoint {
  id: string;
  timestamp: Date;
  message?: string;
  wordCount: number;
  author: string;
  isAutoSave: boolean;
  changes?: { added: number; removed: number };
}

interface DraftVersion {
  id: string;
  name: string;
  description?: string;
  createdAt: Date;
  lastModified: Date;
  wordCount: number;
  author: string;
  isActive: boolean;
  checkpoints: Checkpoint[];
  parentVersionId?: string;
}

const Editor: React.FC = () => {
  const { documentId } = useParams<{ documentId?: string }>();
  const navigate = useNavigate();
  const editorRef = useRef<HTMLDivElement>(null);
  
  // Mobile detection
  const [isMobile, setIsMobile] = useState(false);
  
  useEffect(() => {
    const shouldUseMobile = shouldUseMobileEditor();
    console.log('Device detection:', {
      userAgent: navigator.userAgent,
      viewportWidth: window.innerWidth,
      shouldUseMobile
    });
    setIsMobile(shouldUseMobile);
    
    // Listen for window resize to detect orientation changes
    const handleResize = () => {
      const shouldUseMobileResized = shouldUseMobileEditor();
      console.log('Resize detection:', {
        viewportWidth: window.innerWidth,
        shouldUseMobile: shouldUseMobileResized
      });
      setIsMobile(shouldUseMobileResized);
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  // Document state
  const [document, setDocument] = useState<Document>({
    id: documentId || 'new',
    title: 'Untitled Document',
    content: '',
    lastSaved: null,
    collaborators: [
      {
        id: '1',
        name: 'Sarah Wilson',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b5b5?w=64&h=64&fit=crop&crop=face',
        isOnline: true,
      },
      {
        id: '2',
        name: 'Alex Chen',
        isOnline: false,
      },
    ],
  });

  // Editor state
  const [isAutoSaving, setIsAutoSaving] = useState(false);
  const [activeFormats, setActiveFormats] = useState<Set<string>>(new Set());
  const [isGenerating, setIsGenerating] = useState(false);
  const [showAIPanel, setShowAIPanel] = useState(true);
  const [activeAITab, setActiveAITab] = useState<'ai' | 'outline' | 'comments'>('ai');
  // Removed cursorPosition state to fix jumping cursor issue
  const [showCommandPalette, setShowCommandPalette] = useState(false);
  const [focusMode, setFocusMode] = useState(false);
  const [leftSidebarCollapsed, setLeftSidebarCollapsed] = useState(false);
  const [rightSidebarCollapsed, setRightSidebarCollapsed] = useState(false);
  const [editorLines, setEditorLines] = useState<HTMLElement[]>([]);
  const [selectedText, setSelectedText] = useState<string>('');

  // Enterprise Features State
  const [healthScore, setHealthScore] = useState<WritingHealthScore>({
    overall: 78,
    readingLevel: 82,
    wordiness: 75,
    passiveVoice: 88,
    inclusiveLanguage: 95,
    brandVoice: 70,
  });
  const [healthIssues, setHealthIssues] = useState<HealthIssue[]>([]);
  const [isHealthPanelOpen, setIsHealthPanelOpen] = useState(false);

  // Citation State
  const [isCitationSearchOpen, setIsCitationSearchOpen] = useState(false);
  const [isCitationManagerOpen, setIsCitationManagerOpen] = useState(false);
  const [citations, setCitations] = useState<Citation[]>([]);

  // Version Control State
  const [isVersionControlOpen, setIsVersionControlOpen] = useState(false);
  const [currentVersion, setCurrentVersion] = useState<DraftVersion>({
    id: 'main',
    name: 'Main Draft',
    description: 'Primary working version',
    createdAt: new Date(Date.now() - 86400000),
    lastModified: new Date(),
    wordCount: 0,
    author: 'Current User',
    isActive: true,
    checkpoints: [],
  });
  const [versions, setVersions] = useState<DraftVersion[]>([]);

  // Import State
  const [isImportWizardOpen, setIsImportWizardOpen] = useState(false);

  // Analytics State
  const [isDocumentAnalyticsOpen, setIsDocumentAnalyticsOpen] = useState(false);
  const analyticsService = useAnalyticsService();
  const analyticsStore = useAnalyticsStore();

  // Persona State
  const [isPersonaFeedbackOpen, setIsPersonaFeedbackOpen] = useState(false);
  const personaStore = usePersonaStore();

  // Collaboration State
  const [currentUser] = useState<User>({
    id: 'current-user',
    name: 'You',
    email: '<EMAIL>',
    color: '#3b82f6',
    isOnline: true,
  });
  const [collaborators, setCollaborators] = useState<UserPresence[]>([
    {
      id: 'user1',
      name: 'Sarah Wilson',
      email: '<EMAIL>',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b5b5?w=64&h=64&fit=crop&crop=face',
      color: '#10b981',
      isOnline: true,
      status: 'active',
      currentAction: 'viewing',
      location: { line: 8, character: 12 },
    },
  ]);
  const [connectionStatus] = useState<'connected' | 'connecting' | 'disconnected'>('connected');

  // Calculate reading time
  const readingTime = Math.ceil((document.content.split(' ').filter(Boolean).length) / 200); // 200 words per minute

  // Mock token usage
  const tokenUsage = { used: 5000, limit: 225000 };

  // Initialize Writing Health Analyzer
  const analyzer = new WritingHealthAnalyzer();

  // Mock AI suggestions
  const [suggestions] = useState<AISuggestion[]>([
    {
      id: '1',
      type: 'grammar',
      severity: 'high',
      title: 'Possessive pronoun correction',
      description: 'Consider changing "there" to "their" for proper possession.',
      originalText: 'The team completed there project successfully.',
      suggestedText: 'The team completed their project successfully.',
      line: 3,
    },
    {
      id: '2',
      type: 'style',
      severity: 'medium',
      title: 'Sentence simplification',
      description: 'This sentence could be more concise.',
      originalText: 'In spite of the fact that the project was challenging, it succeeded.',
      suggestedText: 'Despite the challenging project, it succeeded.',
      line: 5,
    },
    {
      id: '3',
      type: 'content',
      severity: 'low',
      title: 'Add supporting details',
      description: 'Consider adding more specific examples to strengthen this argument.',
      originalText: 'AI has many applications in business.',
      suggestedText: 'AI has many applications in business, including customer service automation, predictive analytics, and supply chain optimization.',
      line: 8,
    },
  ]);

  // Auto-save functionality
  useEffect(() => {
    const autoSaveInterval = setInterval(() => {
      if (document.content.trim()) {
        handleAutoSave();
      }
    }, 30000); // Auto-save every 30 seconds

    return () => clearInterval(autoSaveInterval);
  }, [document.content]);

  // Writing Health Analysis
  useEffect(() => {
    const analyzeContent = async () => {
      if (document.content.trim()) {
        const result = analyzer.analyzeText(document.content);
        setHealthScore(result.score);
        setHealthIssues(result.issues);
        
        // Update current version word count
        const wordCount = result.wordCount;
        setCurrentVersion(prev => ({ ...prev, wordCount, lastModified: new Date() }));
      }
    };

    const timeoutId = setTimeout(analyzeContent, 500); // Debounce
    return () => clearTimeout(timeoutId);
  }, [document.content]);

  // Initialize versions
  useEffect(() => {
    setVersions([currentVersion]);
  }, []);

  // Initialize analytics tracking
  useEffect(() => {
    if (documentId && documentId !== 'new') {
      analyticsService.trackDocumentOpened(documentId);
    } else if (documentId === 'new') {
      analyticsService.trackDocumentCreated(document.id, document.title);
    }

    // Cleanup on unmount
    return () => {
      analyticsService.cleanup();
    };
  }, [documentId]);

  // Track content changes for analytics
  useEffect(() => {
    if (document.content && document.id) {
      const wordCount = document.content.trim().split(/\s+/).filter(Boolean).length;
      analyticsService.updateWritingProgress(document.id, wordCount, document.content);
    }
  }, [document.content, document.id]);

  const handleAutoSave = useCallback(async () => {
    setIsAutoSaving(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setDocument(prev => ({ ...prev, lastSaved: new Date() }));
    setIsAutoSaving(false);
  }, []);

  const handleTitleChange = useCallback((title: string) => {
    setDocument(prev => ({ ...prev, title }));
  }, []);

  const handleContentChange = useCallback((e: React.FormEvent<HTMLDivElement>) => {
    const content = e.currentTarget.textContent || '';
    setDocument(prev => ({ ...prev, content }));
  }, []);

  const handleFormatClick = useCallback((format: string) => {
    // Toggle format
    const newFormats = new Set(activeFormats);
    if (newFormats.has(format)) {
      newFormats.delete(format);
    } else {
      newFormats.add(format);
    }
    setActiveFormats(newFormats);
    
    // Apply formatting to selection
    (document as any).execCommand(format, false);
  }, [activeFormats]);

  const handleSuggestionAccept = useCallback((suggestionId: string) => {
    console.log('Accepting suggestion:', suggestionId);
    const suggestion = suggestions.find(s => s.id === suggestionId);
    if (suggestion) {
      analyticsService.trackAISuggestionAccepted(suggestion.type);
    }
    // Implementation for accepting suggestion
  }, [suggestions]);

  const handleSuggestionDismiss = useCallback((suggestionId: string) => {
    console.log('Dismissing suggestion:', suggestionId);
    const suggestion = suggestions.find(s => s.id === suggestionId);
    if (suggestion) {
      analyticsService.trackAISuggestionDismissed(suggestion.type);
    }
    // Implementation for dismissing suggestion
  }, [suggestions]);

  const handleSuggestionRerun = useCallback((suggestionId: string) => {
    console.log('Re-running suggestion:', suggestionId);
    // Implementation for re-running suggestion
  }, []);

  const handleGenerateContent = useCallback(async (mode?: 'fix-its' | 'rephrase' | 'build-from' | 'analyze', options?: any) => {
    setIsGenerating(true);
    console.log('Generating content:', mode, options);
    
    // Track AI generation
    if (mode) {
      analyticsService.trackAIGeneration(mode, true);
    }
    
    // Simulate AI generation
    setTimeout(() => {
      if (mode === 'fix-its') {
        // Add mock suggestions based on current health issues
        const mockSuggestions: AISuggestion[] = healthIssues.slice(0, 3).map(issue => ({
          id: `suggestion-${Date.now()}-${Math.random()}`,
          type: issue.type === 'readingLevel' ? 'style' : 
                issue.type === 'wordiness' ? 'style' :
                issue.type === 'passiveVoice' ? 'grammar' : 'content',
          severity: issue.severity,
          title: `Fix ${issue.type}`,
          description: issue.suggestion,
          originalText: issue.sentence.substring(0, 50) + '...',
          suggestedText: 'Improved version of the text...',
          line: issue.line,
        }));
        console.log('Generated suggestions:', mockSuggestions);
      }
      setIsGenerating(false);
    }, 2000);
  }, [healthIssues]);

  const handleShare = useCallback(() => {
    console.log('Sharing document');
    // Implementation for sharing
  }, []);

  const handleExport = useCallback(() => {
    console.log('Exporting document');
    // Implementation for exporting
  }, []);

  const handleSave = useCallback(async () => {
    await handleAutoSave();
  }, [handleAutoSave]);

  // Enterprise Feature Handlers
  const handleFixHealthIssue = useCallback((issueId: string) => {
    console.log('Fixing health issue:', issueId);
    setHealthIssues(prev => prev.filter(issue => issue.id !== issueId));
  }, []);

  const handleInsertCitation = useCallback((citation: Citation) => {
    const citationText = ` [${citation.authors[0].split(',')[0]} et al., ${citation.year}]`;
    setDocument(prev => ({ ...prev, content: prev.content + citationText }));
    setCitations(prev => {
      const updated = prev.map(c => 
        c.id === citation.id ? { ...c, usageCount: c.usageCount + 1 } : c
      );
      if (!updated.find(c => c.id === citation.id)) {
        updated.push({ ...citation, usageCount: 1, dateAdded: new Date() });
      }
      return updated;
    });
    setIsCitationSearchOpen(false);
  }, []);

  const handleCreateVersion = useCallback((name: string, description?: string) => {
    const newVersion: DraftVersion = {
      id: `version-${Date.now()}`,
      name,
      description,
      createdAt: new Date(),
      lastModified: new Date(),
      wordCount: document.content.split(/\s+/).filter(Boolean).length,
      author: currentUser.name,
      isActive: false,
      checkpoints: [],
    };
    setVersions(prev => [...prev, newVersion]);
  }, [document.content, currentUser.name]);

  const handleSwitchVersion = useCallback((versionId: string) => {
    setVersions(prev => prev.map(v => ({ ...v, isActive: v.id === versionId })));
    const newActiveVersion = versions.find(v => v.id === versionId);
    if (newActiveVersion) {
      setCurrentVersion(newActiveVersion);
    }
  }, [versions]);

  const handleImportComplete = useCallback((files: any[], options: any) => {
    console.log('Import completed:', files, options);
    // Simulate adding imported content
    if (files.length > 0) {
      const importedText = files.map(f => f.extractedText).join('\n\n');
      if (options.mergeIntoDocument) {
        setDocument(prev => ({ 
          ...prev, 
          content: prev.content + '\n\n' + importedText 
        }));
      }
    }
    setIsImportWizardOpen(false);
  }, []);

  // Removed cursor tracking to fix jumping cursor issue

  // Focus on editor when component mounts
  useEffect(() => {
    if (editorRef.current) {
      editorRef.current.focus();
    }
  }, []);

  // Global keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Command palette: Cmd/Ctrl + K
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        setShowCommandPalette(true);
      }
      
      // AI Generate: Cmd/Ctrl + Shift + A
      if ((e.metaKey || e.ctrlKey) && e.shiftKey && e.key === 'A') {
        e.preventDefault();
        handleGenerateContent();
      }
      
      // Save: Cmd/Ctrl + S
      if ((e.metaKey || e.ctrlKey) && e.key === 's') {
        e.preventDefault();
        handleSave();
      }
      
      // Focus Mode: Cmd/Ctrl + Shift + F
      if ((e.metaKey || e.ctrlKey) && e.shiftKey && e.key === 'F') {
        e.preventDefault();
        const newFocusMode = !focusMode;
        setFocusMode(newFocusMode);
        analyticsService.trackFocusModeUsage(newFocusMode);
      }
      
      // Toggle AI Panel: Cmd/Ctrl + J
      if ((e.metaKey || e.ctrlKey) && e.key === 'j') {
        e.preventDefault();
        setShowAIPanel(!showAIPanel);
      }
      
      // Version History: Cmd/Ctrl + Shift + H
      if ((e.metaKey || e.ctrlKey) && e.shiftKey && e.key === 'H') {
        e.preventDefault();
        setIsVersionControlOpen(true);
      }
      
      // Citation Search: Cmd/Ctrl + Shift + C
      if ((e.metaKey || e.ctrlKey) && e.shiftKey && e.key === 'C') {
        e.preventDefault();
        setIsCitationSearchOpen(true);
      }
      
      // Import: Cmd/Ctrl + Shift + I
      if ((e.metaKey || e.ctrlKey) && e.shiftKey && e.key === 'I') {
        e.preventDefault();
        setIsImportWizardOpen(true);
      }
      
      // Persona Feedback: Cmd/Ctrl + Shift + P
      if ((e.metaKey || e.ctrlKey) && e.shiftKey && e.key === 'P') {
        e.preventDefault();
        setIsPersonaFeedbackOpen(true);
      }
    };

    if (typeof window !== 'undefined' && window.document) {
      window.document.addEventListener('keydown', handleKeyDown);
      return () => window.document.removeEventListener('keydown', handleKeyDown);
    }
  }, [focusMode, showAIPanel, handleGenerateContent, handleSave]);

  const handleNavigateToLine = useCallback((lineNumber: number) => {
    if (editorRef.current) {
      const lines = editorRef.current.textContent?.split('\n') || [];
      if (lineNumber <= lines.length) {
        // Scroll to approximate line position
        const lineHeight = 24; // Approximate line height
        const targetY = (lineNumber - 1) * lineHeight;
        editorRef.current.scrollIntoView({ behavior: 'smooth' });
        
        // Focus the editor
        editorRef.current.focus();
        
        // Set cursor to approximate position
        const range = (document as any).createRange();
        const selection = window.getSelection();
        
        if (selection) {
          const textContent = editorRef.current.textContent || '';
          const lineStart = textContent.split('\n').slice(0, lineNumber - 1).join('\n').length + (lineNumber > 1 ? 1 : 0);
          
          try {
            const walker = (document as any).createTreeWalker(
              editorRef.current,
              NodeFilter.SHOW_TEXT,
              null
            );
            
            let charCount = 0;
            let targetNode = editorRef.current;
            let targetOffset = 0;
            
            while (walker.nextNode()) {
              const node = walker.currentNode;
              const nodeLength = node.textContent?.length || 0;
              
              if (charCount + nodeLength >= lineStart) {
                targetNode = node;
                targetOffset = lineStart - charCount;
                break;
              }
              charCount += nodeLength;
            }
            
            range.setStart(targetNode, Math.max(0, targetOffset));
            range.collapse(true);
            selection.removeAllRanges();
            selection.addRange(range);
          } catch (error) {
            console.warn('Could not set cursor position:', error);
          }
        }
      }
    }
  }, []);

  const handleCommand = useCallback((commandId: string) => {
    switch (commandId) {
      case 'ai-generate':
        handleGenerateContent();
        break;
      case 'ai-improve':
        // Implementation for improving writing
        console.log('Improving writing...');
        break;
      case 'ai-grammar':
        // Implementation for grammar check
        console.log('Checking grammar...');
        break;
      case 'save':
        handleSave();
        break;
      case 'share':
        handleShare();
        break;
      case 'export':
        handleExport();
        break;
      case 'new-document':
        // Implementation for new document
        window.location.href = '/editor';
        break;
      case 'bookmark':
        // Implementation for bookmarking
        console.log('Bookmarking document...');
        break;
      case 'focus-mode':
        setFocusMode(!focusMode);
        break;
      case 'citations':
        setIsCitationManagerOpen(true);
        break;
      case 'import':
        setIsImportWizardOpen(true);
        break;
      case 'versions':
        setIsVersionControlOpen(true);
        break;
      case 'writing-health':
        setIsHealthPanelOpen(true);
        break;
      case 'document-analytics':
        setIsDocumentAnalyticsOpen(true);
        break;
      case 'persona-feedback':
        setIsPersonaFeedbackOpen(true);
        break;
      default:
        console.log('Unknown command:', commandId);
    }
  }, [handleGenerateContent, handleSave, handleShare, handleExport, focusMode]);

  const handleAITabChange = useCallback((tab: 'ai' | 'outline' | 'comments') => {
    setActiveAITab(tab);
    if (!showAIPanel) {
      setShowAIPanel(true);
    }
  }, [showAIPanel]);

  // Render mobile editor for mobile devices
  if (isMobile) {
    return (
      <MobileEditor
        documentId={documentId}
        initialContent={document.content}
        onSave={(content) => {
          setDocument(prev => ({ ...prev, content }));
          handleSave();
        }}
        onBack={() => navigate('/app/dashboard')}
      />
    );
  }

  return (
    <div className="h-screen flex bg-gradient-to-br from-slate-50 to-white">
      {/* Left Toolbar */}
      {!focusMode && (
        <div className="flex-shrink-0 z-40">
          <LeftToolbar
            activeTab={activeAITab}
            onTabChange={handleAITabChange}
            onGenerateContent={handleGenerateContent}
            onToggleFocusMode={() => setFocusMode(!focusMode)}
            onOpenCommandPalette={() => setShowCommandPalette(true)}
            isGenerating={isGenerating}
            isCollapsed={leftSidebarCollapsed}
            onToggleCollapse={() => setLeftSidebarCollapsed(!leftSidebarCollapsed)}
          />
        </div>
      )}

      {/* Main Content */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* Editor Header */}
        {!focusMode && (
          <div className="border-b border-slate-200 bg-white relative overflow-visible z-[100]">
            <div className="flex items-center justify-between px-6 py-3 relative overflow-visible">
              <EditorHeader
                title={document.title}
                onTitleChange={handleTitleChange}
                lastSaved={document.lastSaved}
                isAutoSaving={isAutoSaving}
                collaborators={document.collaborators}
                onShare={handleShare}
                onExport={handleExport}
                onSave={handleSave}
                onOpenCommandPalette={() => setShowCommandPalette(true)}
                tokenUsage={tokenUsage}
              />
              
              {/* Quick Actions */}
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setIsPersonaFeedbackOpen(true)}
                  className="flex items-center px-3 py-1.5 text-sm bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                >
                  <UserGroupIcon className="w-4 h-4 mr-1" />
                  Personas
                </button>
                <button
                  onClick={() => setIsImportWizardOpen(true)}
                  className="flex items-center px-3 py-1.5 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <CloudArrowUpIcon className="w-4 h-4 mr-1" />
                  Import
                </button>
                <button
                  onClick={() => setIsCitationManagerOpen(true)}
                  className="flex items-center px-3 py-1.5 text-sm bg-slate-200 text-slate-700 rounded-lg hover:bg-slate-300 transition-colors"
                >
                  <BookOpenIcon className="w-4 h-4 mr-1" />
                  Citations
                </button>
                <button
                  onClick={() => setIsVersionControlOpen(true)}
                  className="flex items-center px-3 py-1.5 text-sm bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  <ClockIcon className="w-4 h-4 mr-1" />
                  Versions
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Editor Toolbar */}
        {!focusMode && (
          <EditorToolbar
            onFormatClick={handleFormatClick}
            activeFormats={activeFormats}
          />
        )}

      {/* Main Editor Layout */}
      <div className="flex-1 flex overflow-hidden">
        {/* Document Editor */}
        <div className="flex-1 flex flex-col relative">
          {/* Writing Area */}
          <div className="flex-1 overflow-y-auto">
            <div className="max-w-4xl mx-auto px-8 py-12">
              {/* Content Editor */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="min-h-screen"
              >
                <div
                  ref={editorRef}
                  contentEditable
                  suppressContentEditableWarning
                  onInput={handleContentChange}
                  onSelect={() => {
                    const selection = window.getSelection();
                    if (selection && selection.toString().trim()) {
                      setSelectedText(selection.toString());
                    }
                  }}
                  onKeyDown={(e) => {
                    // Citation trigger: @@ 
                    if (e.key === '@' && e.target instanceof HTMLElement) {
                      const text = e.target.textContent || '';
                      if (text.endsWith('@')) {
                        setTimeout(() => setIsCitationSearchOpen(true), 100);
                      }
                    }
                  }}
                  className="min-h-96 outline-none text-slate-900 leading-relaxed text-lg focus:ring-0 selection:bg-purple-100 selection:text-purple-900"
                  style={{
                    lineHeight: '1.75',
                    fontFamily: 'Inter, system-ui, sans-serif',
                  }}
                  data-placeholder="Start writing your document here..."
                >
                  {!document.content && (
                    <div className="text-slate-400 pointer-events-none select-none">
                      <p className="text-2xl font-light mb-4">✨ Start writing something amazing...</p>
                      <p className="text-base opacity-75">
                        Press <kbd className="px-2 py-1 bg-slate-200 rounded text-sm font-mono">Cmd+K</kbd> for commands, or just start typing.
                      </p>
                    </div>
                  )}
                </div>
              </motion.div>

              {/* Focus Mode Indicator */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="fixed bottom-8 left-8 lg:left-72 bg-white/90 backdrop-blur-sm rounded-2xl px-4 py-2 shadow-lg border border-slate-200/50"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-sm font-medium text-slate-700">Focus Mode</span>
                  <div className="text-xs text-slate-500">
                    {document.content.split(' ').filter(word => word.length > 0).length} words
                  </div>
                </div>
              </motion.div>
            </div>
          </div>

          {/* Expand button when right sidebar is collapsed - SAME position */}
          <AnimatePresence>
            {(rightSidebarCollapsed || !showAIPanel) && !focusMode && (
              <motion.button
                onClick={() => {
                  if (rightSidebarCollapsed) {
                    setRightSidebarCollapsed(false);
                  }
                  setShowAIPanel(true);
                }}
                className="fixed bottom-4 right-4 w-10 h-10 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg shadow-xl flex items-center justify-center z-50 group"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <ChevronLeftIcon className="w-5 h-5 text-white" />
                <div className="absolute inset-0 bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg" />
              </motion.button>
            )}
          </AnimatePresence>
        </div>

        {/* AI Panel */}
        <AnimatePresence>
          {showAIPanel && !focusMode && !rightSidebarCollapsed && (
            <motion.div
              initial={{ x: '100%', opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              exit={{ x: '100%', opacity: 0 }}
              transition={{ type: 'spring', damping: 25, stiffness: 200 }}
              className="z-50"
            >
              <EnhancedAIPanel
                suggestions={suggestions}
                onAcceptSuggestion={handleSuggestionAccept}
                onDismissSuggestion={handleSuggestionDismiss}
                onRerunSuggestion={handleSuggestionRerun}
                onGenerateContent={handleGenerateContent}
                isGenerating={isGenerating}
                documentContent={document.content}
                selectedText={selectedText}
                onNavigateToLine={handleNavigateToLine}
                activeTab={activeAITab}
                onTabChange={handleAITabChange}
                isCollapsed={rightSidebarCollapsed}
                onToggleCollapse={() => setRightSidebarCollapsed(!rightSidebarCollapsed)}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Status Bar */}
      {!focusMode && (
        <StatusBar
          content={document.content}
          isAutoSaving={isAutoSaving}
          lastSaved={document.lastSaved}
          readingTime={readingTime}
          focusMode={focusMode}
          onToggleFocusMode={() => setFocusMode(!focusMode)}
          writingHealthScore={healthScore.overall}
          documentId={document.id}
        />
      )}

      {/* Command Palette */}
      <CommandPalette
        isOpen={showCommandPalette}
        onClose={() => setShowCommandPalette(false)}
        onCommand={handleCommand}
      />

      {/* Focus Mode Toggle */}
      {focusMode && (
        <motion.button
          onClick={() => setFocusMode(false)}
          className="fixed top-4 right-4 z-50 p-3 bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg border border-slate-200/50 text-slate-600 hover:text-slate-900 transition-colors"
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </motion.button>
        )}

      {/* Enterprise Features */}
      
      {/* Writing Health Badge */}
      <WritingHealthBadge
        score={healthScore}
        onOpenPanel={() => setIsHealthPanelOpen(true)}
        isVisible={!focusMode}
      />

      {/* Writing Health Panel */}
      <WritingHealthPanel
        isOpen={isHealthPanelOpen}
        onClose={() => setIsHealthPanelOpen(false)}
        score={healthScore}
        issues={healthIssues}
        onFixIssue={handleFixHealthIssue}
        onNavigateToLine={handleNavigateToLine}
      />

      {/* Smart Citation Search */}
      <SmartCitationSearch
        isOpen={isCitationSearchOpen}
        onClose={() => setIsCitationSearchOpen(false)}
        onInsertCitation={handleInsertCitation}
      />

      {/* Citation Manager */}
      <CitationManager
        isOpen={isCitationManagerOpen}
        onClose={() => setIsCitationManagerOpen(false)}
        citations={citations}
        onAddCitation={() => setIsCitationSearchOpen(true)}
        onEditCitation={(citation) => console.log('Edit citation:', citation)}
        onDeleteCitation={(id) => setCitations(prev => prev.filter(c => c.id !== id))}
        onInsertCitation={handleInsertCitation}
        onExportBibliography={(style, citations) => console.log('Export:', style, citations)}
      />

      {/* Version Control */}
      <VersionControl
        isOpen={isVersionControlOpen}
        onClose={() => setIsVersionControlOpen(false)}
        currentVersion={currentVersion}
        versions={versions}
        onCreateVersion={handleCreateVersion}
        onSwitchVersion={handleSwitchVersion}
        onMergeVersions={(sourceId, targetId) => console.log('Merge:', sourceId, targetId)}
        onRestoreCheckpoint={(checkpointId) => console.log('Restore:', checkpointId)}
        onCreateCheckpoint={(message) => console.log('Create checkpoint:', message)}
        onPreviewCheckpoint={(checkpointId) => console.log('Preview:', checkpointId)}
      />

      {/* Import Wizard */}
      <ImportWizard
        isOpen={isImportWizardOpen}
        onClose={() => setIsImportWizardOpen(false)}
        onImportComplete={handleImportComplete}
      />

        {/* Document Analytics Panel */}
        <DocumentAnalyticsPanel
          documentId={document.id}
          documentContent={document.content}
          isOpen={isDocumentAnalyticsOpen}
          onClose={() => setIsDocumentAnalyticsOpen(false)}
          onRefresh={() => {
            // Trigger analytics refresh
            analyticsStore.calculateInsights();
            analyticsStore.updateStreak();
            analyticsStore.checkAchievements();
          }}
        />

        {/* Persona Feedback Panel */}
        <PersonaFeedbackPanel
          documentId={document.id}
          documentContent={document.content}
          isOpen={isPersonaFeedbackOpen}
          onClose={() => setIsPersonaFeedbackOpen(false)}
          onGenerateFeedback={() => {
            // Optional callback after feedback generation
            console.log('Persona feedback generated');
          }}
        />

        {/* Collaborative cursors removed to fix cursor jumping issue */}
      </div>
    </div>
  );
};

export default Editor;