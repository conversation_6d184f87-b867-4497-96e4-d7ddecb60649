import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  XMarkIcon,
  HeartIcon,
  BookOpenIcon,
  ChatBubbleBottomCenterTextIcon,
  EyeSlashIcon,
  UserGroupIcon,
  SparklesIcon,
  ChevronRightIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ArrowPathIcon,
} from '@heroicons/react/24/outline';

interface WritingHealthScore {
  overall: number;
  readingLevel: number;
  wordiness: number;
  passiveVoice: number;
  inclusiveLanguage: number;
  brandVoice: number;
}

interface HealthIssue {
  id: string;
  type: 'readingLevel' | 'wordiness' | 'passiveVoice' | 'inclusiveLanguage' | 'brandVoice';
  severity: 'low' | 'medium' | 'high';
  sentence: string;
  suggestion: string;
  line: number;
}

interface WritingHealthPanelProps {
  isOpen: boolean;
  onClose: () => void;
  score: WritingHealthScore;
  issues: HealthIssue[];
  onFixIssue: (issueId: string) => void;
  onNavigateToLine: (line: number) => void;
}

const healthMetrics = [
  {
    key: 'readingLevel' as const,
    label: 'Reading Level',
    description: 'Complexity and accessibility of your writing',
    icon: BookOpenIcon,
    color: 'blue',
    target: 60,
  },
  {
    key: 'wordiness' as const,
    label: 'Clarity',
    description: 'Sentence length and conciseness',
    icon: ChatBubbleBottomCenterTextIcon,
    color: 'green',
    target: 70,
  },
  {
    key: 'passiveVoice' as const,
    label: 'Active Voice',
    description: 'Use of active vs passive voice',
    icon: EyeSlashIcon,
    color: 'purple',
    target: 85,
  },
  {
    key: 'inclusiveLanguage' as const,
    label: 'Inclusive Language',
    description: 'Bias-free and inclusive terminology',
    icon: UserGroupIcon,
    color: 'pink',
    target: 90,
  },
  {
    key: 'brandVoice' as const,
    label: 'Brand Voice',
    description: 'Consistency with your brand tone',
    icon: SparklesIcon,
    color: 'orange',
    target: 80,
  },
];

const WritingHealthPanel: React.FC<WritingHealthPanelProps> = ({
  isOpen,
  onClose,
  score,
  issues,
  onFixIssue,
  onNavigateToLine,
}) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'issues'>('overview');
  const [selectedMetric, setSelectedMetric] = useState<string | null>(null);

  const getScoreColor = (score: number, target: number) => {
    if (score >= target) return 'emerald';
    if (score >= target - 15) return 'yellow';
    return 'red';
  };

  const getScoreStatus = (score: number, target: number) => {
    if (score >= target) return 'excellent';
    if (score >= target - 15) return 'good';
    return 'needs-improvement';
  };

  const filteredIssues = selectedMetric 
    ? issues.filter(issue => issue.type === selectedMetric)
    : issues;

  const issuesByMetric = healthMetrics.reduce((acc, metric) => {
    acc[metric.key] = issues.filter(issue => issue.type === metric.key);
    return acc;
  }, {} as Record<string, HealthIssue[]>);

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40"
            onClick={onClose}
          />

          {/* Panel */}
          <motion.div
            initial={{ opacity: 0, x: 400 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 400 }}
            transition={{ type: "spring", damping: 20, stiffness: 300 }}
            className="fixed right-0 top-0 bottom-0 w-96 bg-white/95 backdrop-blur-xl border-l border-slate-200/50 shadow-2xl z-50 flex flex-col"
          >
            {/* Header */}
            <div className="px-6 py-4 border-b border-slate-200/50 bg-gradient-to-r from-purple-50/50 to-pink-50/50">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                    <HeartIcon className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h2 className="text-lg font-bold text-slate-900">Writing Health</h2>
                    <p className="text-sm text-slate-600">
                      Overall Score: <span className={`font-semibold text-${getScoreColor(score.overall, 80)}-600`}>
                        {Math.round(score.overall)}/100
                      </span>
                    </p>
                  </div>
                </div>
                <button
                  onClick={onClose}
                  className="p-2 text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded-lg transition-colors"
                >
                  <XMarkIcon className="w-5 h-5" />
                </button>
              </div>

              {/* Tabs */}
              <div className="flex mt-4 bg-white/50 rounded-xl p-1">
                <button
                  onClick={() => setActiveTab('overview')}
                  className={`flex-1 px-4 py-2 text-sm font-semibold rounded-lg transition-all ${
                    activeTab === 'overview'
                      ? 'bg-white text-slate-900 shadow-sm'
                      : 'text-slate-600 hover:text-slate-900'
                  }`}
                >
                  Overview
                </button>
                <button
                  onClick={() => setActiveTab('issues')}
                  className={`flex-1 px-4 py-2 text-sm font-semibold rounded-lg transition-all ${
                    activeTab === 'issues'
                      ? 'bg-white text-slate-900 shadow-sm'
                      : 'text-slate-600 hover:text-slate-900'
                  }`}
                >
                  Issues {issues.length > 0 && (
                    <span className="ml-2 px-2 py-0.5 text-xs bg-red-100 text-red-700 rounded-full">
                      {issues.length}
                    </span>
                  )}
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="flex-1 overflow-y-auto">
              <AnimatePresence mode="wait">
                {activeTab === 'overview' ? (
                  <motion.div
                    key="overview"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className="p-6 space-y-6"
                  >
                    {/* Metrics */}
                    {healthMetrics.map((metric, index) => {
                      const metricScore = score[metric.key];
                      const color = getScoreColor(metricScore, metric.target);
                      const status = getScoreStatus(metricScore, metric.target);
                      const IconComponent = metric.icon;
                      const metricIssues = issuesByMetric[metric.key] || [];

                      return (
                        <motion.div
                          key={metric.key}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.1 }}
                          className="bg-white/80 rounded-2xl p-4 border border-slate-200/50 hover:shadow-md transition-all duration-200"
                        >
                          <div className="flex items-start justify-between mb-3">
                            <div className="flex items-center space-x-3">
                              <div className={`w-10 h-10 bg-${metric.color}-100 rounded-xl flex items-center justify-center`}>
                                <IconComponent className={`w-5 h-5 text-${metric.color}-600`} />
                              </div>
                              <div>
                                <h3 className="font-semibold text-slate-900">{metric.label}</h3>
                                <p className="text-sm text-slate-600">{metric.description}</p>
                              </div>
                            </div>
                            <div className="text-right">
                              <div className={`text-lg font-bold text-${color}-600`}>
                                {Math.round(metricScore)}
                              </div>
                              <div className="text-xs text-slate-500">
                                Target: {metric.target}
                              </div>
                            </div>
                          </div>

                          {/* Progress bar */}
                          <div className="relative">
                            <div className="w-full bg-slate-200 rounded-full h-2">
                              <motion.div
                                className={`h-2 rounded-full bg-gradient-to-r from-${color}-500 to-${color}-600`}
                                initial={{ width: 0 }}
                                animate={{ width: `${metricScore}%` }}
                                transition={{ duration: 1, delay: index * 0.1 }}
                              />
                            </div>
                            <div
                              className="absolute top-0 w-0.5 h-2 bg-slate-400"
                              style={{ left: `${metric.target}%` }}
                            />
                          </div>

                          {/* Issues count */}
                          {metricIssues.length > 0 && (
                            <button
                              onClick={() => {
                                setActiveTab('issues');
                                setSelectedMetric(metric.key);
                              }}
                              className="mt-3 flex items-center text-sm text-slate-600 hover:text-slate-900 transition-colors"
                            >
                              <ExclamationTriangleIcon className="w-4 h-4 mr-2" />
                              {metricIssues.length} issue{metricIssues.length !== 1 ? 's' : ''} found
                              <ChevronRightIcon className="w-4 h-4 ml-1" />
                            </button>
                          )}
                        </motion.div>
                      );
                    })}
                  </motion.div>
                ) : (
                  <motion.div
                    key="issues"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className="p-6 space-y-4"
                  >
                    {/* Filter */}
                    <div>
                      <label className="block text-sm font-medium text-slate-700 mb-2">
                        Filter by metric:
                      </label>
                      <select
                        value={selectedMetric || ''}
                        onChange={(e) => setSelectedMetric(e.target.value || null)}
                        className="w-full px-3 py-2 border border-slate-300 rounded-lg text-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      >
                        <option value="">All issues</option>
                        {healthMetrics.map(metric => (
                          <option key={metric.key} value={metric.key}>
                            {metric.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Issues list */}
                    {filteredIssues.length > 0 ? (
                      <div className="space-y-3">
                        {filteredIssues.map((issue, index) => (
                          <motion.div
                            key={issue.id}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: index * 0.05 }}
                            className="bg-white rounded-xl p-4 border border-slate-200 hover:shadow-md transition-all duration-200"
                          >
                            <div className="flex items-start justify-between mb-2">
                              <div className="flex items-center space-x-2">
                                <div className={`w-2 h-2 rounded-full ${
                                  issue.severity === 'high' ? 'bg-red-500' :
                                  issue.severity === 'medium' ? 'bg-yellow-500' : 'bg-blue-500'
                                }`} />
                                <span className="text-sm font-medium text-slate-900">
                                  {healthMetrics.find(m => m.key === issue.type)?.label}
                                </span>
                                <button
                                  onClick={() => onNavigateToLine(issue.line)}
                                  className="text-xs text-slate-500 hover:text-slate-700"
                                >
                                  Line {issue.line}
                                </button>
                              </div>
                              <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                                issue.severity === 'high' ? 'bg-red-100 text-red-700' :
                                issue.severity === 'medium' ? 'bg-yellow-100 text-yellow-700' :
                                'bg-blue-100 text-blue-700'
                              }`}>
                                {issue.severity}
                              </span>
                            </div>

                            <p className="text-sm text-slate-600 mb-2">{issue.sentence}</p>
                            <p className="text-sm text-slate-900 font-medium mb-3">
                              💡 {issue.suggestion}
                            </p>

                            <button
                              onClick={() => onFixIssue(issue.id)}
                              className="flex items-center px-3 py-1.5 bg-gradient-to-r from-purple-500 to-pink-500 text-white text-sm font-medium rounded-lg hover:shadow-md transition-all duration-200"
                            >
                              <ArrowPathIcon className="w-4 h-4 mr-2" />
                              Fix with AI
                            </button>
                          </motion.div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <CheckCircleIcon className="w-12 h-12 text-green-500 mx-auto mb-3" />
                        <h3 className="text-lg font-semibold text-slate-900 mb-2">
                          No issues found!
                        </h3>
                        <p className="text-slate-600">
                          {selectedMetric ? 'This metric looks great.' : 'Your writing health is excellent.'}
                        </p>
                      </div>
                    )}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default WritingHealthPanel;