#!/usr/bin/env python3
"""
Debug 005_missing_features.sql by testing each variable assignment query individually
"""

import asyncio
import os
import asyncpg
from dotenv import load_dotenv

async def test_individual_queries():
    """Test each SELECT INTO query from 005_missing_features.sql individually."""
    
    load_dotenv(".env")
    database_url = os.getenv("DATABASE_URL")
    
    conn = None
    try:
        conn = await asyncpg.connect(database_url, statement_cache_size=0)
        
        print("🔍 Testing individual queries from 005_missing_features.sql")
        print("=" * 60)
        
        # Test user lookups
        user_queries = [
            ("sarah_id", "SELECT id FROM users WHERE firebase_uid = 'firebase_user_001'"),
            ("alex_id", "SELECT id FROM users WHERE firebase_uid = 'firebase_user_002'"),
            ("kim_id", "SELECT id FROM users WHERE firebase_uid = 'firebase_user_003'"),
            ("maya_id", "SELECT id FROM users WHERE firebase_uid = 'firebase_user_004'"),
            ("james_id", "SELECT id FROM users WHERE firebase_uid = 'firebase_user_005'"),
        ]
        
        print("\n👤 Testing user lookups:")
        for var_name, query in user_queries:
            try:
                results = await conn.fetch(query)
                print(f"  {var_name}: {len(results)} rows returned")
                if len(results) > 1:
                    print(f"    ❌ MULTIPLE ROWS! {[r['id'] for r in results]}")
                elif len(results) == 0:
                    print(f"    ❌ NO ROWS!")
                else:
                    print(f"    ✅ OK - {results[0]['id']}")
            except Exception as e:
                print(f"    ❌ ERROR: {e}")
        
        # Test document lookups
        doc_queries = [
            ("sarah_climate_doc_id", "SELECT id FROM documents WHERE title = 'Climate Change Research Paper'"),
            ("sarah_blog_doc_id", "SELECT id FROM documents WHERE title = 'Personal Blog Draft'"),
            ("alex_cafe_doc_id", "SELECT id FROM documents WHERE title = 'The Midnight Café'"),
            ("alex_chars_doc_id", "SELECT id FROM documents WHERE title = 'Character Development Notes'"),
            ("kim_neural_doc_id", "SELECT id FROM documents WHERE title = 'Neural Networks in Medical Diagnosis'"),
            ("kim_conf_doc_id", "SELECT id FROM documents WHERE title = 'Conference Presentation Outline'"),
            ("kim_collab_doc_id", "SELECT id FROM documents WHERE title = 'Industry Research Collaboration'"),
            ("maya_chap1_doc_id", "SELECT id FROM documents WHERE title = 'Chronicles of Aetheria: Chapter 1'"),
            ("maya_world_doc_id", "SELECT id FROM documents WHERE title = 'World Building Bible'"),
            ("maya_blog_doc_id", "SELECT id FROM documents WHERE title = 'Writing Process Blog Series'"),
            ("james_q4_doc_id", "SELECT id FROM documents WHERE title = 'Q4 Marketing Strategy Report'"),
            ("james_launch_doc_id", "SELECT id FROM documents WHERE title = 'Product Launch Proposal'"),
        ]
        
        print("\n📄 Testing document lookups:")
        for var_name, query in doc_queries:
            try:
                results = await conn.fetch(query)
                print(f"  {var_name}: {len(results)} rows returned")
                if len(results) > 1:
                    print(f"    ❌ MULTIPLE ROWS! {[r['id'] for r in results]}")
                elif len(results) == 0:
                    print(f"    ❌ NO ROWS!")
                else:
                    print(f"    ✅ OK - {results[0]['id']}")
            except Exception as e:
                print(f"    ❌ ERROR: {e}")
        
        # Get some document IDs for block testing
        sarah_climate_doc = await conn.fetchval("SELECT id FROM documents WHERE title = 'Climate Change Research Paper' LIMIT 1")
        alex_cafe_doc = await conn.fetchval("SELECT id FROM documents WHERE title = 'The Midnight Café' LIMIT 1")
        kim_neural_doc = await conn.fetchval("SELECT id FROM documents WHERE title = 'Neural Networks in Medical Diagnosis' LIMIT 1")
        maya_chap1_doc = await conn.fetchval("SELECT id FROM documents WHERE title = 'Chronicles of Aetheria: Chapter 1' LIMIT 1")
        james_q4_doc = await conn.fetchval("SELECT id FROM documents WHERE title = 'Q4 Marketing Strategy Report' LIMIT 1")
        
        # Test block lookups
        if all([sarah_climate_doc, alex_cafe_doc, kim_neural_doc, maya_chap1_doc, james_q4_doc]):
            block_queries = [
                ("sarah_climate_para1_id", f"SELECT id FROM blocks WHERE content LIKE 'Climate change represents one of the most pressing%' AND document_id = '{sarah_climate_doc}'"),
                ("alex_cafe_para1_id", f"SELECT id FROM blocks WHERE content LIKE 'Elena had walked past the corner%' AND document_id = '{alex_cafe_doc}'"),
                ("kim_neural_para_id", f"SELECT id FROM blocks WHERE content LIKE 'The integration of artificial neural networks%' AND document_id = '{kim_neural_doc}'"),
                ("maya_chap1_para1_id", f"SELECT id FROM blocks WHERE content LIKE 'The ancient crown of Aetheria lay in fragments%' AND document_id = '{maya_chap1_doc}'"),
                ("james_q4_para_id", f"SELECT id FROM blocks WHERE content LIKE 'Q4 2024 demonstrated exceptional growth%' AND document_id = '{james_q4_doc}'"),
            ]
            
            print("\n📝 Testing block lookups:")
            for var_name, query in block_queries:
                try:
                    results = await conn.fetch(query)
                    print(f"  {var_name}: {len(results)} rows returned")
                    if len(results) > 1:
                        print(f"    ❌ MULTIPLE ROWS! {[r['id'] for r in results]}")
                    elif len(results) == 0:
                        print(f"    ❌ NO ROWS!")
                    else:
                        print(f"    ✅ OK - {results[0]['id']}")
                except Exception as e:
                    print(f"    ❌ ERROR: {e}")
        
        # Test persona lookups that are used in 005_missing_features.sql
        sarah_id = await conn.fetchval("SELECT id FROM users WHERE firebase_uid = 'firebase_user_001' LIMIT 1")
        alex_id = await conn.fetchval("SELECT id FROM users WHERE firebase_uid = 'firebase_user_002' LIMIT 1")
        kim_id = await conn.fetchval("SELECT id FROM users WHERE firebase_uid = 'firebase_user_003' LIMIT 1")
        
        if all([sarah_id, alex_id, kim_id]):
            persona_queries = [
                ("env_researcher", f"SELECT id FROM personas WHERE name = 'Environmental Researcher' AND user_id = '{sarah_id}'"),
                ("urban_fantasy", f"SELECT id FROM personas WHERE name = 'Urban Fantasy Storyteller' AND user_id = '{alex_id}'"),
                ("medical_scholar", f"SELECT id FROM personas WHERE name = 'Medical Research Scholar' AND user_id = '{kim_id}'"),
            ]
            
            print("\n🎭 Testing persona lookups:")
            for var_name, query in persona_queries:
                try:
                    results = await conn.fetch(query)
                    print(f"  {var_name}: {len(results)} rows returned")
                    if len(results) > 1:
                        print(f"    ❌ MULTIPLE ROWS! {[r['id'] for r in results]}")
                    elif len(results) == 0:
                        print(f"    ❌ NO ROWS!")
                    else:
                        print(f"    ✅ OK - {results[0]['id']}")
                except Exception as e:
                    print(f"    ❌ ERROR: {e}")
        
        print("\n" + "=" * 60)
        print("🔍 SUMMARY: Look for any queries returning MULTIPLE ROWS above.")
        print("That's likely the cause of the 'query returned more than one row' error.")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        if conn:
            await conn.close()

if __name__ == "__main__":
    asyncio.run(test_individual_queries())