-- Revisionary Mock Data - Missing Features & Advanced Analytics
-- Part 5: Health metrics, entities, citations, analytics, templates, and advanced features
-- Depends on 001_core_data.sql for user and document references

DO $$
DECLARE
    -- Get user IDs from firebase_uid (from 001_core_data.sql)
    sarah_id UUID;
    alex_id UUID;
    kim_id UUID;
    maya_id UUID;
    james_id UUID;
    
    -- Get document IDs by title (from 001_core_data.sql)
    sarah_climate_doc_id UUID;
    sarah_blog_doc_id UUID;
    alex_cafe_doc_id UUID;
    alex_chars_doc_id UUID;
    kim_neural_doc_id UUID;
    kim_conf_doc_id UUID;
    kim_collab_doc_id UUID;
    maya_chap1_doc_id UUID;
    maya_world_doc_id UUID;
    maya_blog_doc_id UUID;
    james_q4_doc_id UUID;
    james_launch_doc_id UUID;
    
    -- Get block IDs for health issues and entity references
    sarah_climate_para1_id UUID;
    alex_cafe_para1_id UUID;
    kim_neural_para_id UUID;
    maya_chap1_para1_id UUID;
    james_q4_para_id UUID;
    
    -- For capturing new IDs
    aetheria_entity_id UUID;
    lyralei_entity_id UUID;
    eldaron_entity_id UUID;
    temporal_cafe_entity_id UUID;
    elena_entity_id UUID;
    medical_ref_id UUID;
    climate_ref_id UUID;
    business_template_id UUID;
    novel_template_id UUID;
    
BEGIN
    -- Get user IDs from existing users
    SELECT id INTO sarah_id FROM users WHERE firebase_uid = 'firebase_user_001' LIMIT 1;
    SELECT id INTO alex_id FROM users WHERE firebase_uid = 'firebase_user_002' LIMIT 1;
    SELECT id INTO kim_id FROM users WHERE firebase_uid = 'firebase_user_003' LIMIT 1;
    SELECT id INTO maya_id FROM users WHERE firebase_uid = 'firebase_user_004' LIMIT 1;
    SELECT id INTO james_id FROM users WHERE firebase_uid = 'firebase_user_005' LIMIT 1;
    
    -- Get document IDs from existing documents
    SELECT id INTO sarah_climate_doc_id FROM documents WHERE title = 'Climate Change Research Paper' LIMIT 1;
    SELECT id INTO sarah_blog_doc_id FROM documents WHERE title = 'Personal Blog Draft' LIMIT 1;
    SELECT id INTO alex_cafe_doc_id FROM documents WHERE title = 'The Midnight Café' LIMIT 1;
    SELECT id INTO alex_chars_doc_id FROM documents WHERE title = 'Character Development Notes' LIMIT 1;
    SELECT id INTO kim_neural_doc_id FROM documents WHERE title = 'Neural Networks in Medical Diagnosis' LIMIT 1;
    SELECT id INTO kim_conf_doc_id FROM documents WHERE title = 'Conference Presentation Outline' LIMIT 1;
    SELECT id INTO kim_collab_doc_id FROM documents WHERE title = 'Industry Research Collaboration' LIMIT 1;
    SELECT id INTO maya_chap1_doc_id FROM documents WHERE title = 'Chronicles of Aetheria: Chapter 1' LIMIT 1;
    SELECT id INTO maya_world_doc_id FROM documents WHERE title = 'World Building Bible' LIMIT 1;
    SELECT id INTO maya_blog_doc_id FROM documents WHERE title = 'Writing Process Blog Series' LIMIT 1;
    SELECT id INTO james_q4_doc_id FROM documents WHERE title = 'Q4 Marketing Strategy Report' LIMIT 1;
    SELECT id INTO james_launch_doc_id FROM documents WHERE title = 'Product Launch Proposal' LIMIT 1;
    
    -- Get block IDs for health and entity references
    SELECT id INTO sarah_climate_para1_id FROM blocks WHERE content LIKE 'Climate change represents one of the most pressing%' AND document_id = sarah_climate_doc_id LIMIT 1;
    SELECT id INTO alex_cafe_para1_id FROM blocks WHERE content LIKE 'Elena had walked past the corner%' AND document_id = alex_cafe_doc_id LIMIT 1;
    SELECT id INTO kim_neural_para_id FROM blocks WHERE content LIKE 'The integration of artificial neural networks%' AND document_id = kim_neural_doc_id LIMIT 1;
    SELECT id INTO maya_chap1_para1_id FROM blocks WHERE content LIKE 'The ancient crown of Aetheria lay in fragments%' AND document_id = maya_chap1_doc_id LIMIT 1;
    SELECT id INTO james_q4_para_id FROM blocks WHERE content LIKE 'Q4 2024 demonstrated exceptional growth%' AND document_id = james_q4_doc_id LIMIT 1;

    -- =================================================================
    -- SCORING ALGORITHMS - Additional algorithm versions beyond v1.0
    -- =================================================================
    
    INSERT INTO scoring_algorithms (version, algorithm_name, description, grammar_weights, style_weights, structure_weights, content_weights, document_type_adjustments, is_active) 
    VALUES
    ('1.1', 'Enhanced Academic Scoring', 'Improved algorithm with better academic writing assessment', 
     '{"base_weight": 0.25, "citation_bonus": 0.05, "methodology_boost": 0.03}'::jsonb,
     '{"base_weight": 0.20, "passive_voice_penalty": -0.02, "technical_vocabulary_tolerance": 0.03}'::jsonb,
     '{"base_weight": 0.25, "logical_flow_emphasis": 0.04, "section_organization": 0.02}'::jsonb,
     '{"base_weight": 0.30, "evidence_strength": 0.05, "depth_analysis": 0.03}'::jsonb,
     '{"academic": {"multiplier": 1.1, "citation_requirement": true}, "research": {"multiplier": 1.05, "methodology_emphasis": true}}'::jsonb, false),
    
    ('1.2', 'Creative Writing Optimizer', 'Specialized scoring for creative and narrative content',
     '{"base_weight": 0.15, "dialogue_mechanics": 0.04, "narrative_grammar": 0.02}'::jsonb,
     '{"base_weight": 0.35, "voice_consistency": 0.08, "atmospheric_language": 0.06, "metaphor_quality": 0.04}'::jsonb,
     '{"base_weight": 0.25, "pacing_analysis": 0.05, "scene_structure": 0.03}'::jsonb,
     '{"base_weight": 0.25, "character_development": 0.06, "emotional_resonance": 0.05}'::jsonb,
     '{"creative": {"multiplier": 1.15, "atmosphere_bonus": 0.1}, "novel": {"multiplier": 1.1, "character_emphasis": true}}'::jsonb, false),
    
    ('2.0', 'AI-Enhanced Multi-Modal Scoring', 'Next-generation algorithm with AI-powered content analysis',
     '{"base_weight": 0.20, "semantic_analysis": 0.05, "context_grammar": 0.03}'::jsonb,
     '{"base_weight": 0.25, "ai_tone_detection": 0.07, "brand_voice_matching": 0.05}'::jsonb,
     '{"base_weight": 0.25, "ai_flow_analysis": 0.06, "audience_alignment": 0.04}'::jsonb,
     '{"base_weight": 0.30, "semantic_depth": 0.08, "contextual_relevance": 0.06}'::jsonb,
     '{"all": {"ai_enhancement": true, "semantic_bonus": 0.05}, "experimental": {"tone_detection": true, "brand_matching": true}}'::jsonb, false),
    
    ('1.3', 'Business Communication Specialist', 'Optimized for professional and business writing',
     '{"base_weight": 0.20, "business_language": 0.03, "clarity_emphasis": 0.04}'::jsonb,
     '{"base_weight": 0.30, "executive_tone": 0.06, "action_orientation": 0.05, "conciseness": 0.04}'::jsonb,
     '{"base_weight": 0.30, "roi_presentation": 0.05, "stakeholder_focus": 0.04}'::jsonb,
     '{"base_weight": 0.20, "data_integration": 0.05, "strategic_insight": 0.04}'::jsonb,
     '{"professional": {"multiplier": 1.1, "executive_bonus": 0.08}, "business": {"multiplier": 1.15, "roi_emphasis": true}}'::jsonb, false);

    -- =================================================================
    -- HEALTH METRICS - Document health and quality analysis
    -- =================================================================
    
    -- Sarah's climate research paper health metrics
    INSERT INTO health_metrics (block_id, user_id, overall_score, readability_score, clarity_score, voice_consistency_score, inclusivity_score, brand_alignment_score, flesch_kincaid_grade, flesch_reading_ease, passive_voice_percentage, avg_sentence_length, complex_words_percentage, processing_time_ms) 
    VALUES
    (sarah_climate_para1_id, sarah_id, 85.2, 78.5, 88.0, 85.0, 92.0, NULL, 12.8, 48.5, 15.2, 22.4, 18.7, 2150),
    
    -- Alex's creative writing health metrics  
    (alex_cafe_para1_id, alex_id, 91.8, 85.0, 94.0, 89.0, 88.0, NULL, 9.2, 65.8, 8.3, 18.6, 12.4, 1890),
    
    -- Dr. Kim's medical research health metrics
    (kim_neural_para_id, kim_id, 94.5, 82.0, 96.0, 94.0, 90.0, NULL, 14.6, 42.1, 18.9, 25.8, 22.3, 3200),
    
    -- Maya's fantasy novel health metrics
    (maya_chap1_para1_id, maya_id, 88.7, 87.0, 91.0, 92.0, 85.0, NULL, 8.4, 68.2, 6.7, 16.9, 11.8, 1650),
    
    -- James's business report health metrics
    (james_q4_para_id, james_id, 92.3, 89.0, 95.0, 91.0, 89.0, 94.0, 11.5, 52.4, 12.1, 19.8, 15.6, 1420);

    -- =================================================================
    -- HEALTH ISSUES - Specific problems identified in documents
    -- =================================================================
    
    INSERT INTO health_issues (health_metric_id, block_id, issue_type, severity, category, description, position, suggested_fix, is_resolved, confidence) 
    VALUES
    ((SELECT id FROM health_metrics WHERE block_id = sarah_climate_para1_id LIMIT 1), sarah_climate_para1_id, 'readability', 'medium', 'sentence_length',
     'Sentence length averaging 28 words may challenge general readers', '{"start": 0, "end": 150}'::jsonb, 
     'Consider breaking complex sentences into shorter, more digestible segments while maintaining academic rigor', false, 0.78),
    
    ((SELECT id FROM health_metrics WHERE block_id = sarah_climate_para1_id LIMIT 1), sarah_climate_para1_id, 'clarity', 'low', 'terminology',
     'Technical term "anthropogenic" used without definition', '{"start": 85, "end": 98}'::jsonb,
     'Add brief definition in parentheses: "anthropogenic (human-caused)"', false, 0.85),
    
    ((SELECT id FROM health_metrics WHERE block_id = alex_cafe_para1_id LIMIT 1), alex_cafe_para1_id, 'voice', 'low', 'perspective_consistency',
     'Narrative perspective shifts slightly in third paragraph', '{"start": 200, "end": 350}'::jsonb,
     'Maintain consistent third-person limited perspective throughout', true, 0.72),
    
    ((SELECT id FROM health_metrics WHERE block_id = kim_neural_para_id LIMIT 1), kim_neural_para_id, 'clarity', 'medium', 'methodology_presentation',
     'Methodology section could benefit from visual flowchart', '{"start": 150, "end": 400}'::jsonb,
     'Consider adding a process diagram to illustrate the neural network training pipeline', false, 0.81),
    
    ((SELECT id FROM health_metrics WHERE block_id = maya_chap1_para1_id LIMIT 1), maya_chap1_para1_id, 'voice', 'low', 'terminology_density',
     'Fantasy terminology density peaks in opening paragraph', '{"start": 0, "end": 200}'::jsonb,
     'Balance world-building exposition with accessible language for reader onboarding', false, 0.69),
    
    ((SELECT id FROM health_metrics WHERE block_id = james_q4_para_id LIMIT 1), james_q4_para_id, 'brand', 'low', 'formatting_consistency',
     'Metric presentation style varies across sections', '{"start": 100, "end": 300}'::jsonb,
     'Standardize percentage and currency formatting throughout document', true, 0.88);

    -- =================================================================
    -- ENTITIES - Characters, locations, concepts extracted from documents
    -- =================================================================
    
    -- Entities from Maya's fantasy world
    INSERT INTO entities (document_id, user_id, name, entity_type, description, attributes, first_mentioned_block_id, last_mentioned_block_id, mention_count, importance_score) 
    VALUES
    (maya_chap1_doc_id, maya_id, 'Aetheria', 'location', 'The primary fantasy realm consisting of seven kingdoms', 
     '{"realm_type": "fantasy", "kingdoms": 7, "government": "High King system", "magic_level": "high", "geography": "diverse continents"}'::jsonb, 
     maya_chap1_para1_id, maya_chap1_para1_id, 3, 0.95)
    RETURNING id INTO aetheria_entity_id;
    
    INSERT INTO entities (document_id, user_id, name, entity_type, description, attributes, first_mentioned_block_id, last_mentioned_block_id, mention_count, importance_score)
    VALUES
    (maya_chap1_doc_id, maya_id, 'Princess Lyralei', 'character', 'Protagonist princess dealing with the crown destruction', 
     '{"title": "Princess", "kingdom": "central realm", "role": "protagonist", "age_range": "young adult", "magical_ability": "moderate"}'::jsonb, 
     maya_chap1_para1_id, maya_chap1_para1_id, 2, 0.98)
    RETURNING id INTO lyralei_entity_id;
    
    INSERT INTO entities (document_id, user_id, name, entity_type, description, attributes, first_mentioned_block_id, last_mentioned_block_id, mention_count, importance_score)
    VALUES
    (maya_chap1_doc_id, maya_id, 'Master Eldaron', 'character', 'Wise advisor who understands the prophecy', 
     '{"title": "Master", "role": "advisor", "specialization": "prophecy", "age": "elder", "wisdom_level": "high"}'::jsonb, 
     maya_chap1_para1_id, maya_chap1_para1_id, 1, 0.82)
    RETURNING id INTO eldaron_entity_id;
    
    INSERT INTO entities (document_id, user_id, name, entity_type, description, attributes, first_mentioned_block_id, last_mentioned_block_id, mention_count, importance_score)
    VALUES
    (maya_world_doc_id, maya_id, 'Ethereal Essence', 'concept', 'The magical energy system that powers Aetheria', 
     '{"energy_type": "magical", "source": "cosmic", "accessibility": "trained individuals", "depletion": "renewable", "manifestations": ["healing", "elemental", "prophetic"]}'::jsonb, 
     maya_chap1_para1_id, maya_chap1_para1_id, 5, 0.89);
    
    -- Entities from Alex's urban fantasy
    INSERT INTO entities (document_id, user_id, name, entity_type, description, attributes, first_mentioned_block_id, last_mentioned_block_id, mention_count, importance_score)
    VALUES
    (alex_cafe_doc_id, alex_id, 'Temporal Grounds', 'location', 'Mysterious café that appears only at midnight', 
     '{"establishment_type": "café", "operating_hours": "midnight only", "clientele": "supernatural", "atmosphere": "liminal", "reality_level": "between worlds"}'::jsonb, 
     alex_cafe_para1_id, alex_cafe_para1_id, 4, 0.93)
    RETURNING id INTO temporal_cafe_entity_id;
    
    INSERT INTO entities (document_id, user_id, name, entity_type, description, attributes, first_mentioned_block_id, last_mentioned_block_id, mention_count, importance_score)
    VALUES
    (alex_cafe_doc_id, alex_id, 'Elena', 'character', 'Protagonist who discovers the mysterious café', 
     '{"role": "protagonist", "supernatural_awareness": "awakening", "occupation": "unknown", "age_range": "adult", "intuition_level": "high"}'::jsonb, 
     alex_cafe_para1_id, alex_cafe_para1_id, 6, 0.96)
    RETURNING id INTO elena_entity_id;
    
    -- Entities from Dr. Kim's research
    INSERT INTO entities (document_id, user_id, name, entity_type, description, attributes, first_mentioned_block_id, last_mentioned_block_id, mention_count, importance_score)
    VALUES
    (kim_neural_doc_id, kim_id, 'Convolutional Neural Networks', 'concept', 'Deep learning architecture for medical image analysis', 
     '{"abbreviation": "CNN", "domain": "medical imaging", "accuracy_range": "85-95%", "applications": ["radiology", "pathology", "dermatology"], "training_requirements": "large datasets"}'::jsonb, 
     kim_neural_para_id, kim_neural_para_id, 8, 0.91),
    
    (kim_neural_doc_id, kim_id, 'Healthcare AI Implementation', 'concept', 'Process of integrating AI systems into clinical workflows', 
     '{"stages": ["validation", "integration", "training", "monitoring"], "challenges": ["regulatory", "ethical", "technical"], "success_factors": ["physician_adoption", "patient_safety", "accuracy"]}'::jsonb, 
     kim_neural_para_id, kim_neural_para_id, 3, 0.84);

    -- =================================================================
    -- ENTITY RELATIONSHIPS - Connections between entities
    -- =================================================================
    
    INSERT INTO entity_relationships (source_entity_id, target_entity_id, relationship_type, description, strength, context_block_id, is_bidirectional) 
    VALUES
    (lyralei_entity_id, aetheria_entity_id, 'hierarchical', 'Princess Lyralei is heir to the throne of Aetheria', 0.95, maya_chap1_para1_id, false),
    (eldaron_entity_id, lyralei_entity_id, 'professional', 'Master Eldaron serves as advisor to Princess Lyralei', 0.88, maya_chap1_para1_id, false),
    (aetheria_entity_id, eldaron_entity_id, 'geographic', 'Master Eldaron resides in the capital of Aetheria', 0.75, maya_chap1_para1_id, false),
    (elena_entity_id, temporal_cafe_entity_id, 'causal', 'Elena discovers and becomes connected to Temporal Grounds', 0.92, alex_cafe_para1_id, false),
    (lyralei_entity_id, eldaron_entity_id, 'friendship', 'Deep mutual respect and trust between princess and advisor', 0.85, maya_chap1_para1_id, true);

    -- =================================================================
    -- CONSISTENCY VIOLATIONS - Narrative/factual conflicts detected
    -- =================================================================
    
    INSERT INTO consistency_violations (document_id, entity_id, violation_type, severity, description, conflicting_blocks, suggested_resolution, is_resolved) 
    VALUES
    (maya_chap1_doc_id, aetheria_entity_id, 'attribute_conflict', 'minor', 
     'Aetheria described as having "seven realms" in chapter 1 but "seven kingdoms" in world bible. Conflicting references found in multiple locations create terminology inconsistency that could confuse readers.', 
     ARRAY[maya_chap1_para1_id]::UUID[], 
     'Standardize terminology to "Seven Realms of Aetheria" throughout all documents', false),
    
    (maya_world_doc_id, lyralei_entity_id, 'timeline_conflict', 'moderate', 
     'Princess Lyralei age referenced as "young adult" but coronation timeline suggests she is a minor. Age references conflict with succession law timeline creating narrative inconsistency.', 
     ARRAY[maya_chap1_para1_id]::UUID[], 
     'Clarify age as 20, with coronation delayed due to crown destruction', false),
    
    (alex_cafe_doc_id, temporal_cafe_entity_id, 'location_conflict', 'minor', 
     'Café described as "corner establishment" but also "appears anywhere needed". Location descriptions conflict regarding the magical rules of manifestation and fixed vs flexible positioning.', 
     ARRAY[alex_cafe_para1_id]::UUID[], 
     'Establish that café has a fixed anchor point but can extend influence', true);

    -- =================================================================
    -- CITATIONS - Academic and web citations in documents
    -- =================================================================
    
    INSERT INTO citations (document_id, block_id, citation_key, citation_type, title, authors, publication_year, journal_name, volume, issue, page_numbers, doi, url, isbn, citation_style, formatted_citation) 
    VALUES
    (sarah_climate_doc_id, sarah_climate_para1_id, 'johnson2023sea', 'journal_article', 
     'Global Sea Level Rise: Past, Present, and Future Projections', 
     '{"Johnson, M.K.", "Liu, X.", "Thompson, R.J."}', 2023, 'Climate Science Quarterly', '45', '3', '234-251', 
     '10.1016/j.clisci.2023.03.015', NULL, NULL, 'apa', 
     'Johnson, M. K., Liu, X., & Thompson, R. J. (2023). Global sea level rise: Past, present, and future projections. Climate Science Quarterly, 45(3), 234-251. https://doi.org/10.1016/j.clisci.2023.03.015'),
    
    (sarah_climate_doc_id, sarah_climate_para1_id, 'ipcc2023synthesis', 'report', 
     'Climate Change 2023: Synthesis Report', 
     '{"IPCC Working Group"}', 2023, NULL, NULL, NULL, NULL, NULL, 
     'https://www.ipcc.ch/report/ar6/syr/', NULL, 'apa', 
     'IPCC Working Group. (2023). Climate Change 2023: Synthesis Report. Intergovernmental Panel on Climate Change. https://www.ipcc.ch/report/ar6/syr/'),
    
    (kim_neural_doc_id, kim_neural_para_id, 'chen2022deep', 'journal_article', 
     'Deep Learning in Medical Image Analysis: A Comprehensive Survey', 
     '{"Chen, H.", "Patel, S.", "Kumar, A.", "Rodriguez, M."}', 2022, 'Medical AI Review', '12', '4', '145-189', 
     '10.1089/mai.2022.0156', NULL, NULL, 'ieee', 
     'H. Chen, S. Patel, A. Kumar, and M. Rodriguez, "Deep learning in medical image analysis: A comprehensive survey," Medical AI Review, vol. 12, no. 4, pp. 145-189, 2022, doi: 10.1089/mai.2022.0156.'),
    
    (kim_neural_doc_id, kim_neural_para_id, 'wang2023improving', 'conference_paper', 
     'Improving Diagnostic Accuracy with Ensemble Neural Networks', 
     '{"Wang, L.", "Davis, K."}', 2023, 'Proceedings of Medical AI Conference', NULL, NULL, '78-84', 
     '10.1145/3583678.3583689', NULL, NULL, 'ieee', 
     'L. Wang and K. Davis, "Improving diagnostic accuracy with ensemble neural networks," in Proc. Medical AI Conf., 2023, pp. 78-84, doi: 10.1145/3583678.3583689.'),
    
    (kim_collab_doc_id, kim_neural_para_id, 'martinez2022ai', 'book', 
     'Artificial Intelligence in Healthcare: Implementation and Ethics', 
     '{"Martinez, C.", "Thompson, D.K."}', 2022, NULL, NULL, NULL, '120-145', NULL, NULL, '978-0-123456-78-9', 'apa', 
     'Martinez, C., & Thompson, D. K. (2022). Artificial Intelligence in Healthcare: Implementation and Ethics (2nd ed.). Academic Press.'),
    
    (james_q4_doc_id, james_q4_para_id, 'mri2024trends', 'website', 
     'Digital Marketing Trends 2024: Analytics and Performance', 
     '{"Marketing Research Institute"}', 2024, NULL, NULL, NULL, NULL, NULL, 
     'https://marketingresearch.org/trends-2024-analytics', NULL, 'apa', 
     'Marketing Research Institute. (2024, January 15). Digital marketing trends 2024: Analytics and performance. https://marketingresearch.org/trends-2024-analytics');

    -- =================================================================
    -- REFERENCE LIBRARY - User's personal reference collections
    -- =================================================================
    
    INSERT INTO reference_library (user_id, title, authors, citation_type, notes, metadata, tags, usage_count, is_favorite) 
    VALUES
    (kim_id, 'Medical AI Research Collection', '{"Various Authors"}', 'journal_article', 
     'Curated references for neural networks in healthcare applications', 
     '{"specializations": ["medical imaging", "diagnostic AI", "clinical implementation"], "years_covered": "2018-2024", "quality_filter": "peer_reviewed_only"}'::jsonb, 
     '{"medical AI", "neural networks", "healthcare", "diagnostics"}', 47, true)
    RETURNING id INTO medical_ref_id;
    
    INSERT INTO reference_library (user_id, title, authors, citation_type, notes, metadata, tags, usage_count, is_favorite)
    VALUES
    (sarah_id, 'Climate Science Core References', '{"IPCC", "Various Researchers"}', 'report', 
     'Essential papers and reports for climate change research', 
     '{"focus_areas": ["sea level rise", "coastal impacts", "policy implications"], "sources": ["IPCC", "peer_reviewed_journals", "government_reports"], "geographic_scope": "global"}'::jsonb, 
     '{"climate change", "sea level", "coastal vulnerability", "policy"}', 23, true)
    RETURNING id INTO climate_ref_id;
    
    INSERT INTO reference_library (user_id, title, authors, citation_type, notes, metadata, tags, usage_count, is_favorite)
    VALUES
    (maya_id, 'Fantasy World-Building Resources', '{"Tolkien, J.R.R.", "Sanderson, Brandon", "Le Guin, Ursula K."}', 'book', 
     'References for creating consistent fantasy worlds and magic systems', 
     '{"topics": ["mythology", "world_building", "fantasy_literature", "magic_systems"], "authors": ["Tolkien", "Sanderson", "Le Guin"], "analysis_focus": "narrative_consistency"}'::jsonb, 
     '{"fantasy", "world building", "mythology", "magic systems", "literature"}', 15, true),
    
    (james_id, 'Business Strategy & Marketing Analytics', '{"Harvard Business Review", "McKinsey & Company"}', 'journal_article', 
     'Key resources for strategic business communication and marketing analysis', 
     '{"domains": ["marketing_analytics", "business_strategy", "performance_metrics"], "industry_focus": ["technology", "digital_marketing"], "methodologies": ["data_driven", "ROI_focused"]}'::jsonb, 
     '{"business strategy", "marketing analytics", "ROI", "performance metrics"}', 31, false),
    
    (alex_id, 'Urban Fantasy & Supernatural Fiction', '{"Gaiman, Neil", "Butcher, Jim", "Harris, Charlaine"}', 'book', 
     'Inspiration and analysis for contemporary fantasy writing', 
     '{"subgenres": ["urban fantasy", "magical realism", "supernatural thriller"], "themes": ["liminal spaces", "hidden worlds", "ordinary_extraordinary"], "craft_focus": "atmosphere_building"}'::jsonb, 
     '{"urban fantasy", "magical realism", "atmosphere", "contemporary fantasy"}', 12, true);

    -- =================================================================
    -- AUDIENCE ANALYSIS - Document audience feedback analysis
    -- =================================================================
    
    INSERT INTO audience_analysis (document_id, persona_ids, overall_appeal_score, audience_alignment, analysis_summary, optimization_suggestions) 
    VALUES
    (sarah_climate_doc_id, 
     ARRAY[(SELECT id FROM personas WHERE name = 'Environmental Researcher' AND user_id = sarah_id LIMIT 1)]::UUID[], 
     0.89, 
     '{"academic_researchers": {"appeal": 0.92, "clarity": 4.2, "relevance": 4.7}, "policy_makers": {"appeal": 0.85, "utility": 4.3, "actionability": 4.1}}'::jsonb, 
     'Strong academic reception with high policy relevance. Methodology approval excellent but accessibility for policy makers could be improved.',
     '["Add executive summary for policy makers", "Include more visual data representations", "Consider companion brief for general public"]'::jsonb),
    
    (alex_cafe_doc_id, 
     ARRAY[(SELECT id FROM personas WHERE name = 'Urban Fantasy Storyteller' AND user_id = alex_id LIMIT 1)]::UUID[], 
     0.85, 
     '{"urban_fantasy_readers": {"appeal": 0.88, "atmosphere": 4.8, "engagement": 4.6}, "young_adult": {"appeal": 0.82, "accessibility": 4.4, "pacing": 4.1}}'::jsonb, 
     'Excellent atmospheric writing with strong mystery engagement. Character development and pacing could be enhanced for broader appeal.',
     '["Expand sensory descriptions", "Develop Elena backstory hints", "Consider chapter breaks for pacing"]'::jsonb),
    
    (kim_neural_doc_id, 
     ARRAY[(SELECT id FROM personas WHERE name = 'Medical Research Scholar' AND user_id = kim_id LIMIT 1)]::UUID[], 
     0.93, 
     '{"medical_professionals": {"appeal": 0.95, "technical_accuracy": 4.9, "clinical_relevance": 4.6}, "ai_researchers": {"appeal": 0.91, "methodology": 4.8, "contribution": 4.7}}'::jsonb, 
     'Outstanding technical accuracy and research contribution. High potential for peer review and citation. Implementation focus could be strengthened.',
     '["Add implementation cost analysis", "Include more case studies", "Expand ethical considerations section"]'::jsonb);

    -- =================================================================
    -- PERSONA FEEDBACK - Effectiveness scoring for user personas
    -- =================================================================
    
    INSERT INTO persona_feedback (block_id, persona_id, session_id, comprehension_score, engagement_score, emotional_score, style_score, overall_score, feedback_text, specific_issues, suggestions, confidence, processing_time_ms, model_used, tokens_used) 
    VALUES
    (kim_neural_para_id, (SELECT id FROM personas WHERE name = 'Medical Research Scholar' AND user_id = kim_id LIMIT 1), gen_random_uuid(), 0.94, 0.88, 0.82, 0.96, 0.90, 
     'Excellent technical accuracy and methodology focus. Well-suited for peer review submission. Could benefit from more clinical implementation examples.',
     '["accessibility for practitioners", "implementation cost considerations"]'::jsonb, 
     '["Include more clinical implementation examples", "Add cost-benefit analysis perspective", "Consider interdisciplinary collaboration angles"]'::jsonb, 
     0.94, 2850, 'gpt-4', 145),
    
    (maya_chap1_para1_id, (SELECT id FROM personas WHERE name = 'Epic Fantasy Narrator' AND user_id = maya_id LIMIT 1), gen_random_uuid(), 0.89, 0.92, 0.87, 0.91, 0.90, 
     'Strong world-building depth and magical consistency. Narrative voice is compelling. Character emotional depth could be enhanced.',
     '["character emotional development", "pacing variation needed"]'::jsonb, 
     '["Develop character internal monologue", "Add more sensory world-building details", "Include foreshadowing elements"]'::jsonb, 
     0.89, 2150, 'gpt-4', 128),
    
    (james_q4_para_id, (SELECT id FROM personas WHERE name = 'Executive Strategist' AND user_id = james_id LIMIT 1), gen_random_uuid(), 0.92, 0.86, 0.85, 0.94, 0.89, 
     'Strong ROI focus and strategic clarity. Data presentation is excellent. Risk analysis and competitive context need strengthening.',
     '["risk analysis depth", "competitive landscape missing"]'::jsonb, 
     '["Add competitive differentiation analysis", "Include risk mitigation strategies", "Expand market opportunity sizing"]'::jsonb, 
     0.92, 1950, 'gpt-4', 108),
    
    (sarah_climate_para1_id, (SELECT id FROM personas WHERE name = 'Environmental Researcher' AND user_id = sarah_id LIMIT 1), gen_random_uuid(), 0.87, 0.84, 0.89, 0.85, 0.86, 
     'Good policy relevance and evidence-based approach. Solution focus and stakeholder engagement could be improved.',
     '["solution orientation lacking", "stakeholder perspective needed"]'::jsonb, 
     '["Include more solution-oriented content", "Add stakeholder impact analysis", "Consider implementation pathways"]'::jsonb, 
     0.87, 2200, 'gpt-4', 132),
    
    (alex_cafe_para1_id, (SELECT id FROM personas WHERE name = 'Urban Fantasy Storyteller' AND user_id = alex_id LIMIT 1), gen_random_uuid(), 0.91, 0.93, 0.88, 0.89, 0.90, 
     'Excellent atmospheric building and mystery development. Contemporary setting works well. Character development and supernatural rules need clarification.',
     '["character development depth", "supernatural system rules"]'::jsonb, 
     '["Develop Elena personal stakes", "Establish supernatural world rules", "Add more sensory atmosphere details"]'::jsonb, 
     0.91, 1850, 'gpt-4', 115);

    -- =================================================================
    -- EXPORT JOBS - Document export/download requests
    -- =================================================================
    
    INSERT INTO export_jobs (user_id, document_id, format, status, file_size, file_url, options, processing_started_at, processing_completed_at, expires_at) 
    VALUES
    (kim_id, kim_neural_doc_id, 'pdf', 'completed', 2847392, 
     'https://storage.supabase.co/exports/neural_networks_medical_diagnosis_20240625.pdf', 
     '{"page_count": 24, "include_citations": true, "formatting": "academic", "header_footer": true, "table_of_contents": true}'::jsonb, 
     NOW() - INTERVAL '2 hours', NOW() - INTERVAL '1 hour 45 minutes', NOW() + INTERVAL '7 days'),
    
    (james_id, james_q4_doc_id, 'docx', 'completed', 1543288, 
     'https://storage.supabase.co/exports/q4_marketing_strategy_20240625.docx', 
     '{"page_count": 18, "include_charts": true, "formatting": "business", "executive_summary": true, "appendices": true}'::jsonb, 
     NOW() - INTERVAL '3 hours', NOW() - INTERVAL '2 hours 30 minutes', NOW() + INTERVAL '7 days'),
    
    (maya_id, maya_chap1_doc_id, 'epub', 'completed', 892156, 
     'https://storage.supabase.co/exports/chronicles_aetheria_chapter1_20240625.epub', 
     '{"word_count": 3200, "include_metadata": true, "formatting": "ebook", "chapter_breaks": true, "font_embedding": true}'::jsonb, 
     NOW() - INTERVAL '1 day', NOW() - INTERVAL '23 hours', NOW() + INTERVAL '14 days'),
    
    (sarah_id, sarah_climate_doc_id, 'latex', 'processing', NULL, NULL, 
     '{"target_journal": "Climate Science Quarterly", "citation_style": "apa", "include_figures": true, "bibliography_separate": true}'::jsonb, 
     NOW() - INTERVAL '30 minutes', NULL, NOW() + INTERVAL '3 days'),
    
    (alex_id, alex_cafe_doc_id, 'html', 'completed', 156789, 
     'https://storage.supabase.co/exports/midnight_cafe_20240625.html', 
     '{"word_count": 2100, "include_styling": true, "formatting": "web", "responsive": true, "social_meta": true}'::jsonb, 
     NOW() - INTERVAL '4 hours', NOW() - INTERVAL '3 hours 45 minutes', NOW() + INTERVAL '7 days'),
    
    (maya_id, maya_world_doc_id, 'markdown', 'failed', NULL, NULL, 
     '{"target_size": 15728640, "max_size": 10485760, "retry_count": 2}'::jsonb, 
     NOW() - INTERVAL '6 hours', NULL, NOW() + INTERVAL '1 day');

    -- =================================================================
    -- TEMPLATES - Reusable document templates
    -- =================================================================
    
    INSERT INTO templates (name, description, category, document_type, structure, metadata, tags, created_by, is_public, usage_count) 
    VALUES
    ('Academic Research Paper Template', 'Standard structure for peer-reviewed academic papers with proper sections and citation format', 'research_paper', 'academic',
     '{"sections": ["abstract", "introduction", "literature_review", "methodology", "results", "discussion", "conclusion", "references"], "formatting": {"citation_style": "apa", "line_spacing": "double", "font": "Times New Roman 12pt"}}'::jsonb, 
     '{"target_audience": "academic", "estimated_length": "5000-8000 words", "complexity": "high", "discipline": "general"}'::jsonb, 
     '{"academic", "research", "peer-review", "apa", "methodology"}', kim_id, true, 23)
    RETURNING id INTO business_template_id;
    
    INSERT INTO templates (name, description, category, document_type, structure, metadata, tags, created_by, is_public, usage_count)
    VALUES
    ('Business Strategy Report Template', 'Executive-level business strategy document with analysis and recommendations', 'business_plan', 'professional',
     '{"sections": ["executive_summary", "situation_analysis", "strategic_options", "recommendations", "implementation_plan", "risk_assessment", "appendices"], "formatting": {"style": "professional", "charts": "required", "executive_focus": true}}'::jsonb, 
     '{"target_audience": "executives", "estimated_length": "3000-5000 words", "complexity": "medium-high", "focus": "strategic"}'::jsonb, 
     '{"business", "strategy", "executive", "analysis", "recommendations"}', james_id, true, 18),
    
    ('Epic Fantasy Novel Template', 'Chapter-based structure for epic fantasy novels with world-building elements', 'novel', 'creative',
     '{"structure": ["prologue", "part_1", "part_2", "part_3", "epilogue"], "chapters_per_part": 8, "elements": ["character_arcs", "world_building", "magic_system", "political_intrigue"], "formatting": {"style": "manuscript", "chapter_headers": true}}'::jsonb, 
     '{"target_audience": "fantasy_readers", "estimated_length": "************ words", "complexity": "medium", "subgenre": "epic_fantasy"}'::jsonb, 
     '{"fantasy", "novel", "epic", "world-building", "magic system"}', maya_id, true, 12)
    RETURNING id INTO novel_template_id;
    
    INSERT INTO templates (name, description, category, document_type, structure, metadata, tags, created_by, is_public, usage_count)
    VALUES
    ('Urban Fantasy Short Story Template', 'Structure for contemporary fantasy short stories in urban settings', 'short_story', 'creative',
     '{"structure": ["hook", "inciting_incident", "rising_action", "climax", "resolution"], "elements": ["modern_setting", "supernatural_element", "character_discovery", "atmospheric_description"], "formatting": {"style": "literary", "length": "short_form"}}'::jsonb, 
     '{"target_audience": "fantasy_readers", "estimated_length": "2000-5000 words", "complexity": "medium", "subgenre": "urban_fantasy"}'::jsonb, 
     '{"urban fantasy", "short story", "contemporary", "supernatural", "atmosphere"}', alex_id, true, 8),
    
    ('Climate Research Policy Brief', 'Template for translating climate research into policy recommendations', 'report', 'academic',
     '{"sections": ["key_findings", "policy_implications", "recommendations", "implementation_pathways", "supporting_evidence"], "formatting": {"style": "policy", "length": "brief", "audience": "policymakers"}}'::jsonb, 
     '{"target_audience": "policymakers", "estimated_length": "1500-3000 words", "complexity": "medium", "focus": "actionable"}'::jsonb, 
     '{"climate", "policy", "research", "brief", "recommendations"}', sarah_id, true, 15),
    
    ('Medical Conference Presentation', 'Template for medical research presentations at academic conferences', 'article', 'professional',
     '{"structure": ["title_slide", "background", "methods", "results", "clinical_implications", "future_research", "questions"], "formatting": {"style": "presentation", "slides": "15-20", "timing": "20_minutes"}}'::jsonb, 
     '{"target_audience": "medical_professionals", "estimated_duration": "20 minutes", "complexity": "high", "focus": "clinical"}'::jsonb, 
     '{"medical", "conference", "presentation", "research", "clinical"}', kim_id, false, 5);

    -- =================================================================
    -- TOKEN USAGE DAILY - Daily API token consumption aggregations
    -- =================================================================
    
    INSERT INTO token_usage_daily (user_id, date, tokens_total, tokens_by_model, tokens_by_agent, operations_count, documents_edited) 
    VALUES
    -- Dr. Kim's usage (professional tier - high research activity)
    (kim_id, CURRENT_DATE - INTERVAL '7 days', 15420, 
     '{"gpt-4": 12340, "gpt-3.5-turbo": 3080}'::jsonb, 
     '{"suggestions": 4200, "research": 3850, "citations": 2100, "health_metrics": 1890, "summaries": 3380}'::jsonb, 45, 3),
    
    (kim_id, CURRENT_DATE - INTERVAL '6 days', 18760, 
     '{"gpt-4": 15680, "gpt-3.5-turbo": 3080}'::jsonb, 
     '{"suggestions": 5100, "research": 4200, "citations": 2860, "health_metrics": 2100, "summaries": 4500}'::jsonb, 52, 4),
    
    (kim_id, CURRENT_DATE - INTERVAL '5 days', 12890, 
     '{"gpt-4": 10120, "gpt-3.5-turbo": 2770}'::jsonb, 
     '{"suggestions": 3450, "research": 3100, "citations": 1890, "health_metrics": 1650, "summaries": 2800}'::jsonb, 38, 2),
    
    -- Maya's usage (professional tier - creative writing)
    (maya_id, CURRENT_DATE - INTERVAL '10 days', 13250, 
     '{"gpt-4": 10980, "gpt-3.5-turbo": 2270}'::jsonb, 
     '{"suggestions": 4200, "personas": 3100, "world_building": 2850, "summaries": 2050, "consistency": 1050}'::jsonb, 42, 2),
    
    (maya_id, CURRENT_DATE - INTERVAL '8 days', 16780, 
     '{"gpt-4": 13540, "gpt-3.5-turbo": 3240}'::jsonb, 
     '{"suggestions": 5100, "personas": 3890, "world_building": 3200, "summaries": 2680, "consistency": 1910}'::jsonb, 48, 3),
    
    (maya_id, CURRENT_DATE - INTERVAL '3 days', 14560, 
     '{"gpt-4": 11890, "gpt-3.5-turbo": 2670}'::jsonb, 
     '{"suggestions": 4650, "personas": 3200, "world_building": 2980, "summaries": 2180, "consistency": 1550}'::jsonb, 44, 2),
    
    -- James's usage (studio tier - business documents)
    (james_id, CURRENT_DATE - INTERVAL '14 days', 22340, 
     '{"gpt-4": 18760, "gpt-3.5-turbo": 3580}'::jsonb, 
     '{"suggestions": 6800, "business_analysis": 5200, "export": 3100, "templates": 2840, "summaries": 4400}'::jsonb, 58, 4),
    
    (james_id, CURRENT_DATE - INTERVAL '7 days', 19680, 
     '{"gpt-4": 16200, "gpt-3.5-turbo": 3480}'::jsonb, 
     '{"suggestions": 5890, "business_analysis": 4650, "export": 2780, "templates": 2560, "summaries": 3800}'::jsonb, 51, 3),
    
    (james_id, CURRENT_DATE - INTERVAL '2 days', 17250, 
     '{"gpt-4": 14100, "gpt-3.5-turbo": 3150}'::jsonb, 
     '{"suggestions": 5200, "business_analysis": 4100, "export": 2450, "templates": 2300, "summaries": 3200}'::jsonb, 47, 3),
    
    -- Sarah's usage (free tier - limited but consistent)
    (sarah_id, CURRENT_DATE - INTERVAL '12 days', 4850, 
     '{"gpt-3.5-turbo": 4850}'::jsonb, 
     '{"suggestions": 2100, "research": 1850, "summaries": 900}'::jsonb, 18, 1),
    
    (sarah_id, CURRENT_DATE - INTERVAL '5 days', 5680, 
     '{"gpt-3.5-turbo": 5680}'::jsonb, 
     '{"suggestions": 2450, "research": 2180, "summaries": 1050}'::jsonb, 22, 2),
    
    (sarah_id, CURRENT_DATE - INTERVAL '1 day', 3920, 
     '{"gpt-3.5-turbo": 3920}'::jsonb, 
     '{"suggestions": 1680, "research": 1540, "summaries": 700}'::jsonb, 15, 1),
    
    -- Alex's usage (free tier - creative focus)
    (alex_id, CURRENT_DATE - INTERVAL '9 days', 4200, 
     '{"gpt-3.5-turbo": 4200}'::jsonb, 
     '{"suggestions": 1890, "creative": 1650, "summaries": 660}'::jsonb, 16, 1),
    
    (alex_id, CURRENT_DATE - INTERVAL '4 days', 5430, 
     '{"gpt-3.5-turbo": 5430}'::jsonb, 
     '{"suggestions": 2200, "creative": 2180, "summaries": 1050}'::jsonb, 21, 2),
    
    (alex_id, CURRENT_DATE - INTERVAL '1 day', 3650, 
     '{"gpt-3.5-turbo": 3650}'::jsonb, 
     '{"suggestions": 1580, "creative": 1420, "summaries": 650}'::jsonb, 14, 1);

    -- =================================================================
    -- AGENT USAGE STATS - AI agent performance metrics
    -- =================================================================
    
    INSERT INTO agent_usage_stats (agent_id, user_id, date, invocations, total_processing_time_ms, suggestions_generated, suggestions_accepted, suggestions_rejected, avg_confidence, error_count, tokens_used) 
    VALUES
    ((SELECT id FROM custom_agents WHERE name = 'Medical Terminology Validator' AND user_id = kim_id LIMIT 1), kim_id, CURRENT_DATE, 156, 444600000, 148, 135, 13, 0.91, 8, 12850),
    
    ((SELECT id FROM custom_agents WHERE name = 'Research Structure Optimizer' AND user_id = kim_id LIMIT 1), kim_id, CURRENT_DATE - INTERVAL '1 day', 89, 373800000, 84, 73, 11, 0.87, 5, 8950),
    
    ((SELECT id FROM custom_agents WHERE name = 'World-Building Consistency Checker' AND user_id = maya_id LIMIT 1), maya_id, CURRENT_DATE, 234, 853100000, 221, 208, 13, 0.94, 13, 18750),
    
    ((SELECT id FROM custom_agents WHERE name = 'Dialogue Enhancement Specialist' AND user_id = maya_id LIMIT 1), maya_id, CURRENT_DATE - INTERVAL '1 day', 178, 530440000, 165, 145, 20, 0.88, 13, 14200),
    
    ((SELECT id FROM custom_agents WHERE name = 'Executive Summary Generator' AND user_id = james_id LIMIT 1), james_id, CURRENT_DATE, 67, 361800000, 64, 61, 3, 0.96, 3, 11450),
    
    ((SELECT id FROM custom_agents WHERE name = 'Data Storytelling Assistant' AND user_id = james_id LIMIT 1), james_id, CURRENT_DATE - INTERVAL '2 days', 92, 441600000, 88, 79, 9, 0.89, 4, 13200),
    
    ((SELECT id FROM custom_agents WHERE name = 'Climate Data Interpreter' AND user_id = sarah_id LIMIT 1), sarah_id, CURRENT_DATE - INTERVAL '1 day', 43, 137600000, 39, 33, 6, 0.84, 4, 5680),
    
    ((SELECT id FROM custom_agents WHERE name = 'Atmospheric Enhancement Agent' AND user_id = alex_id LIMIT 1), alex_id, CURRENT_DATE, 78, 206700000, 72, 62, 10, 0.86, 6, 7850);

END $$;