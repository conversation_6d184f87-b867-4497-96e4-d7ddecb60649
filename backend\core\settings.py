"""
Revisionary Backend Configuration System

Purpose: Centralized configuration management using Pydantic Settings
- Environment variable validation and type conversion
- Default values and validation rules
- Supabase, Redis, and LLM API configuration
- Development vs production settings management

Features:
- Type-safe configuration loading from environment variables
- Validation of required settings
- Default values for development
- Structured configuration for all services
"""

import os
from typing import List, Optional
from pydantic import Field, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Main application settings."""
    
    # Environment
    env: str = Field("development", env="ENV")
    debug: bool = Field(False, env="DEBUG")
    log_level: str = Field("INFO", env="LOG_LEVEL")
    
    # Database settings
    supabase_url: str = Field(..., env="SUPABASE_URL")
    supabase_key: str = Field(..., env="SUPABASE_KEY") 
    supabase_service_key: str = Field("PLACEHOLDER_SERVICE_KEY_FROM_DASHBOARD", env="SUPABASE_SERVICE_KEY")
    database_url: str = Field(..., env="DATABASE_URL")
    
    # Redis settings
    redis_url: str = Field("redis://localhost:6379/0", env="REDIS_URL")
    redis_password: Optional[str] = Field(None, env="REDIS_PASSWORD")
    
    # Auth settings
    firebase_project_id: str = Field("revisionary-prod", env="FIREBASE_PROJECT_ID")
    firebase_credentials_path: Optional[str] = Field(None, env="FIREBASE_CREDENTIALS_PATH")
    jwt_secret_key: str = Field("development-secret-key-change-in-production", env="JWT_SECRET_KEY")
    jwt_algorithm: str = Field("HS256", env="JWT_ALGORITHM")
    jwt_expiration_hours: int = Field(24, env="JWT_EXPIRATION_HOURS")
    
    # LLM settings (optional for initial setup)
    openai_api_key: str = Field("PLACEHOLDER_OPENAI_API_KEY", env="OPENAI_API_KEY")
    google_ai_api_key: str = Field("PLACEHOLDER_GOOGLE_AI_API_KEY", env="GOOGLE_AI_API_KEY")
    anthropic_api_key: str = Field("PLACEHOLDER_ANTHROPIC_API_KEY", env="ANTHROPIC_API_KEY")
    
    # API settings
    api_host: str = Field("127.0.0.1", env="API_HOST")
    api_port: int = Field(8000, env="API_PORT")
    api_workers: int = Field(1, env="API_WORKERS")
    api_timeout_keep_alive: int = Field(5, env="API_TIMEOUT_KEEP_ALIVE")
    api_timeout_graceful_shutdown: int = Field(10, env="API_TIMEOUT_GRACEFUL_SHUTDOWN")
    cors_origins: List[str] = Field(
        ["http://localhost:3000", "http://localhost:5173"],
        env="CORS_ORIGINS"
    )
    
    # Mock settings
    use_mock_data: bool = Field(False, env="USE_MOCK_DATA")
    use_mock_llm: bool = Field(False, env="USE_MOCK_LLM")
    use_mock_auth: bool = Field(False, env="USE_MOCK_AUTH")
    use_mock_redis: bool = Field(False, env="USE_MOCK_REDIS")
    
    # File upload settings
    max_file_size_mb: int = Field(10, env="MAX_FILE_SIZE_MB")
    allowed_file_types: List[str] = Field(
        ["txt", "md", "docx", "pdf"],
        env="ALLOWED_FILE_TYPES"
    )
    
    # Monitoring
    sentry_dsn: Optional[str] = Field(None, env="SENTRY_DSN")
    prometheus_enabled: bool = Field(False, env="PROMETHEUS_ENABLED")
    prometheus_port: int = Field(9090, env="PROMETHEUS_PORT")
    
    # Computed properties for backward compatibility
    @property
    def database(self):
        return type('DatabaseSettings', (), {
            'supabase_url': self.supabase_url,
            'supabase_key': self.supabase_key,
            'supabase_service_key': self.supabase_service_key,
            'database_url': self.database_url,
        })()
    
    @property
    def redis(self):
        return type('RedisSettings', (), {
            'redis_url': self.redis_url,
            'redis_password': self.redis_password,
        })()
    
    @property
    def auth(self):
        return type('AuthSettings', (), {
            'firebase_project_id': self.firebase_project_id,
            'firebase_credentials_path': self.firebase_credentials_path,
            'jwt_secret_key': self.jwt_secret_key,
            'jwt_algorithm': self.jwt_algorithm,
            'jwt_expiration_hours': self.jwt_expiration_hours,
        })()
    
    @property
    def api(self):
        return type('APISettings', (), {
            'host': self.api_host,
            'port': self.api_port,
            'cors_origins': self.cors_origins,
            'cors_credentials': True,
            'cors_methods': ["*"],
            'cors_headers': ["*"],
        })()
    
    @property
    def mock(self):
        return type('MockSettings', (), {
            'use_mock_data': self.use_mock_data,
            'use_mock_llm': self.use_mock_llm,
            'use_mock_auth': self.use_mock_auth,
            'use_mock_redis': self.use_mock_redis,
        })()
    
    @validator('cors_origins', pre=True)
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            # Handle string representation of list
            if v.startswith('[') and v.endswith(']'):
                import json
                return json.loads(v.replace("'", '"'))
            return [v]
        return v
    
    @validator('log_level')
    def validate_log_level(cls, v):
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'Log level must be one of: {valid_levels}')
        return v.upper()
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# Global settings instance
def get_settings() -> Settings:
    """Get application settings instance."""
    return Settings()


# Create settings instance
settings = get_settings()