#!/usr/bin/env python3
"""
Clear mock data from database in proper foreign key order
"""

import asyncio
import asyncpg
from dotenv import load_dotenv
import os

async def clear_mock_data():
    print("🧹 Clearing existing mock data...")
    
    load_dotenv(".env")
    connection_string = os.getenv("DATABASE_URL")
    
    if not connection_string:
        print("❌ DATABASE_URL not found in .env file")
        return False
    
    conn = None
    try:
        conn = await asyncpg.connect(connection_string, statement_cache_size=0)
        print("✅ Connected to database")
        
        # Delete in reverse dependency order to avoid foreign key violations
        clear_tables = [
            # Child tables first (those with foreign keys)
            'agent_style_rules',
            'agent_usage_stats', 
            'usage_events',
            'writing_challenges',
            'writing_achievements',
            'user_writing_stats',
            'document_scores',
            'persona_feedback',
            'audience_analysis',
            'health_issues',
            'health_metrics',
            'entity_relationships',
            'consistency_violations',
            'entities',
            'citations',
            'reference_library',
            'export_jobs',
            'comments',
            'collaborations',
            'summaries',
            'suggestions',
            'versions',
            'custom_agents',
            'personas',
            
            # Then blocks (referenced by many tables above)
            'blocks',
            
            # Then documents (referenced by blocks)
            'documents',
            
            # Finally users (referenced by documents and many other tables)
            'users',
            
            # Other independent tables
            'templates',
            'token_usage_daily',
        ]
        
        deleted_counts = {}
        
        for table in clear_tables:
            try:
                # Only delete rows that look like mock data (with known firebase_uid patterns)
                if table == 'users':
                    result = await conn.execute("""
                        DELETE FROM users 
                        WHERE firebase_uid LIKE 'firebase_user_%'
                    """)
                else:
                    result = await conn.execute(f"DELETE FROM {table}")
                
                count = int(result.split()[-1]) if result.split()[-1].isdigit() else 0
                if count > 0:
                    deleted_counts[table] = count
                    print(f"  🗑️  {table}: {count} records deleted")
                    
            except Exception as table_error:
                print(f"  ⚠️  {table}: {table_error}")
                continue
        
        total_deleted = sum(deleted_counts.values())
        print(f"\n✅ Mock data cleared successfully!")
        print(f"📊 Total records deleted: {total_deleted}")
        print(f"📋 Tables cleared: {len(deleted_counts)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to clear mock data: {e}")
        return False
    finally:
        if conn:
            await conn.close()

if __name__ == "__main__":
    success = asyncio.run(clear_mock_data())
    if not success:
        exit(1)