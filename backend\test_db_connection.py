import asyncio
import asyncpg
from dotenv import load_dotenv
import os

async def test_connection():
    print("=" * 50)
    print("   SUPABASE CONNECTION TEST (v3)")
    print("=" * 50)
    
    load_dotenv(".env")
    
    # Print all relevant environment variables for debugging
    print("🔍 ENVIRONMENT VALIDATION:")
    print("-" * 30)
    
    connection_string = os.getenv("DATABASE_URL")
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY")
    
    if not connection_string:
        print("❌ ERROR: DATABASE_URL not found in your .env file!")
        return False
    
    print(f"✅ DATABASE_URL: {connection_string}")
    print(f"✅ SUPABASE_URL: {supabase_url}")
    print(f"✅ SUPABASE_KEY: {supabase_key[:20]}...{supabase_key[-10:] if supabase_key else 'None'}")
    print()
    print("🔌 Attempting to connect...")
    
    try:
        conn = await asyncpg.connect(connection_string, statement_cache_size=0)
        print("✅ CONNECTION SUCCESSFUL!")
        
        version = await conn.fetchval("SELECT version()")
        print(f"✅ PostgreSQL version: {version.split(',')[0]}")
        
        await conn.close()
        print("✅ Connection closed successfully.")
        return True
        
    except Exception as e:
        print(f"❌ CONNECTION FAILED: {e}")
        print(f"Error type: {type(e).__name__}")
        if "getaddrinfo failed" in str(e).lower():
            print("\n🔧 This is a DNS error. Your computer cannot find the server.")
            print("   Please check your internet connection and firewall settings.")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_connection())
    print("=" * 50)
    if success:
        print("🎉 Database connection is working.")
    else:
        print("❌ Please fix the connection issue before proceeding.")
    print("=" * 50)
